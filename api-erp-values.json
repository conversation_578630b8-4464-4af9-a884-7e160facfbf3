["/hxl.erp.app.calendar.find", "/hxl.erp.app.forecastweather.find", "/hxl.erp.iauth.update", "/hxl.erp.authority.find", "/hxl.erp.roleauthority.export", "/hxl.erp.authority.search", "/hxl.erp.bms.merchant.find", "/hxl.erp.bms.merchant.update", "/hxl.erp.businessarea.updatename", "/hxl.erp.businessarea.read", "/hxl.erp.businessarea.store.detail.find", "/hxl.erp.businessarea.detail.find", "/hxl.erp.businessarea.store.import", "/hxl.erp.businessarea.store.export", "/hxl.erp.businessarea.export", "/hxl.erp.businessarea.store.find", "/hxl.erp.businessarea.update", "/hxl.erp.businessarea.readbyareacode", "/hxl.erp.businessarea.import", "/hxl.erp.businessarea.find", "/hxl.erp.businessareastoretemplate.download", "/hxl.erp.businessarea.hrs.find", "/hxl.erp.businessarea.child.find", "/hxl.erp.businessarea.template.download", "/hxl.erp.businessarea.save", "/hxl.erp.businessarea.delete", "/hxl.erp.businessarea.short.read", "/hxl.erp.businessscopecategory.update", "/hxl.erp.businessscopecategory.save", "/hxl.erp.businessscopecategory.find", "/hxl.erp.businessscopecategory.delete", "/hxl.erp.businessscope.batchdelete", "/hxl.erp.businessscope.store.find", "/hxl.erp.businessscope.heart.check", "/hxl.erp.businessscope.reuploadama", "/hxl.erp.businessscope.heart.beat", "/hxl.erp.businessscope.initsysdata.update", "/hxl.erp.businessscope.read", "/hxl.erp.businessscope.syncama", "/hxl.erp.businessscope.short.find", "/hxl.erp.businessscope.batchupdatev2", "/hxl.erp.businessscope.item.export", "/hxl.erp.businessscope.org.check", "/hxl.erp.businessscope.update", "/hxl.erp.businessscope.heart.delete", "/hxl.erp.businessscope.item.download", "/hxl.erp.businessscope.itemabc.update", "/hxl.erp.businessscope.item.import", "/hxl.erp.businessscope.find", "/hxl.erp.businessscope.item.save", "/hxl.erp.businessscope.delete", "/hxl.erp.businessscope.uploadama", "/hxl.erp.businessscope.item.page", "/hxl.erp.businessscope.store.item.batchexport", "/hxl.erp.businessscope.syncamaone", "/hxl.erp.businessscope.store.item.page", "/hxl.erp.businessscope.batchupdate", "/hxl.erp.businessscope.store.download", "/hxl.erp.businessscope.item.delete", "/hxl.erp.businessscope.save", "/hxl.erp.businessscope.batchexport", "/hxl.erp.business.scope.backup.init", "/hxl.erp.business.scope.backup.batchexport", "/hxl.erp.business.scope.backup.read", "/hxl.erp.business.scope.backup.back", "/hxl.erp.business.scope.backup.page", "/hxl.erp.cargo.owner.conf.relation.pageforwms", "/hxl.erp.cargo.owner.import", "/hxl.erp.cargo.owner.log.page", "/hxl.erp.cargo.owner.save", "/hxl.erp.cargo.owner.template.download", "/hxl.erp.cargo.owner.page", "/hxl.erp.cargo.owner.enabled", "/hxl.erp.cargo.owner.disabled", "/hxl.cargo.owner.orgid.find", "/hxl.erp.cargo.owner.pageforwms", "/hxl.scm.cargo.orgid.find", "/hxl.erp.cargo.owner.export", "/hxl.erp.cargo.owner.findOrgCode", "/hxl.erp.cargo.owner.pageforinner", "/hxl.erp.cargo.owner.batchdelete", "/hxl.erp.center.association.itembrand.find", "/hxl.erp.center.itemcategory.find", "/hxl.erp.center.abnormalcategory.find", "/hxl.erp.center.qualityreportposorder.itemdetail", "/hxl.erp.center.itembrand.read", "/hxl.erp.center.supplier.distributecheck", "/hxl.erp.center.org.find", "/hxl.erp.center.item.centerid.update", "/hxl.erp.checksupplier.page", "/hxl.erp.checksupplier.save", "/hxl.erp.checksupplier.check", "/hxl.erp.checksupplier.delete", "/hxl.erp.checksupplier.read", "/hxl.erp.checksupplier.update", "/hxl.erp.checksupplier.download", "/hxl.erp.checksupplier.import", "/hxl.erp.clienthistory.page", "/hxl.erp.clienthistory.read", "/hxl.erp.clientcategory.update", "/hxl.erp.clientcategory.find", "/hxl.erp.clientcategory.save", "/hxl.erp.clientcategory.delete", "/hxl.erp.clientcategory.findwithclient", "/hxl.erp.client.read", "/hxl.erp.client.updatecreditLine", "/hxl.erp.clienttemplate.download", "/hxl.erp.clientupdatetemplate.download", "/hxl.erp.client.findbyupdatetime", "/hxl.erp.client.export", "/hxl.erp.client.balance.read", "/hxl.erp.client.import", "/hxl.erp.clientnametemplate.download", "/hxl.erp.client.update", "/hxl.erp.client.storerelationship", "/hxl.erp.client.code.get", "/hxl.erp.client.update.import", "/hxl.erp.client.batchupdate.count", "/hxl.erp.client.bms.merchantstatus", "/hxl.erp.clientname.import", "/hxl.erp.client.delete", "/hxl.erp.client.batchupdate", "/hxl.erp.client.save", "/hxl.erp.client.page", "/hxl.erp.company.token.refresh", "/hxl.erp.company.token.get", "/hxl.erp.company.refreshtoken.get", "/hxl.erp.company.init", "/hxl.erp.company.find", "/hxl.erp.company.account.find", "/hxl.erp.companyinvoice.page", "/hxl.erp.companyinvoice.find", "/hxl.erp.companyinvoice.account.find", "/hxl.erp.companyinvoice.save", "/hxl.erp.companyinvoice.delete", "/hxl.erp.companyinvoice.export", "/hxl.erp.companyinvoice.read", "/hxl.erp.contract.page", "/hxl.erp.contract.whitelist.create", "/hxl.erp.contract.whitelist.export", "/hxl.erp.contract.enum", "/hxl.erp.contract.whitelist.page", "/hxl.erp.sign.contract.job", "/hxl.erp.contract.read", "/hxl.erp.contract.triggertask", "/hxl.erp.sign.contract.callback", "/hxl.erp.contract.download", "/hxl.erp.contract.view", "/hxl.erp.contract.create", "/hxl.erp.sign.certification.check", "/hxl.erp.contract.list", "/hxl.erp.contract.template.check", "/hxl.erp.contract.template.read", "/hxl.erp.contract.template.update", "/hxl.erp.contract.template.acquire", "/hxl.erp.contract.template.delete", "/hxl.erp.contract.template.page", "/hxl.erp.contract.template.save", "/hxl.erp.customattribute.save", "/hxl.erp.customattribute.delete", "/hxl.erp.customattribute.sequence.update", "/hxl.erp.customattributename.check.find", "/hxl.erp.customattribute.update", "/hxl.erp.customattribute.initial", "/hxl.erp.customattribute.show.find", "/hxl.erp.customattribute.find", "/hxl.erp.delivery.cargo.owner.conf.batchdelete", "/hxl.erp.delivery.cargo.owner.conf.save", "/hxl.erp.delivery.cargo.owner.conf.readbystoreIds", "/hxl.erp.delivery.cargo.owner.conf.import", "/hxl.erp.delivery.cargo.owner.conf.export", "/hxl.erp.delivery.cargo.owner.conf.sync", "/hxl.erp.delivery.cargo.owner.find", "/hxl.erp.delivery.cargo.owner.org.find", "/hxl.erp.delivery.cargo.owner.conf.page", "/hxl.erp.delivery.cargo.owner.conf.copy", "/hxl.erp.delivery.cargo.owner.conf.wholesale", "/hxl.erp.delivery.cargo.owner.conf.template.download", "/hxl.erp.delivery.cargo.owner.conf.log.page", "/hxl.erp.delivery.cargo.owner.conf.pageforwms", "/hxl.erp.delivery.cargo.owner.conf.update", "/hxl.erp.file.compress", "/hxl.erp.file.find", "/hxl.erp.file.ocr.handwriting", "/hxl.erp.file.businesslicensecheck", "/hxl.erp.supplierpriceadjust.file.upload", "/hxl.erp.file.delete", "/hxl.erp.file.ocr", "/hxl.erp.file.ocr.sign", "/hxl.erp.file.upload", "/hxl.erp.file.ocr.ignore", "/hxl.erp.file.checkexist", "/hxl.erp.font.upload", "/hxl.erp.font.file.upload", "/hxl.erp.font.find", "/hxl.erp.fsms.userAuthority.find", "/hxl.erp.hardwarecategory.delete", "/hxl.erp.hardwarecategory.update", "/hxl.erp.hardwarecategory.find", "/hxl.erp.hardwarecategory.save", "/hxl.erp.hardware.find", "/hxl.erp.hardware.delete", "/hxl.erp.hardware.save", "/hxl.erp.hardware.update", "/hxl.erp.iauth.generateoneuserstore", "/hxl.erp.iauth.generatealluserstore", "/hxl.erp.iauth.generatepartuserstore", "/hxl.erp.investmentorder.import", "/hxl.erp.investmentorder.update", "/hxl.erp.investmentorder.audit", "/hxl.erp.investmentorder.save", "/hxl.erp.investmentorder.read", "/hxl.erp.investmentorder.batchdelete", "/hxl.erp.investmentorder.page", "/hxl.erp.investmentorder.download", "/hxl.erp.investmentorder.invalid", "/hxl.erp.investmentsubjectcategory.delete", "/hxl.erp.investmentsubjectcategory.init", "/hxl.erp.investmentsubjectcategory.save", "/hxl.erp.investmentsubjectcategory.update", "/hxl.erp.investmentsubjectcategory.find", "/hxl.erp.investmentsubject.init", "/hxl.erp.investmentsubject.update", "/hxl.erp.investmentsubject.find", "/hxl.erp.investmentsubject.save", "/hxl.erp.investmentsubject.delete", "/hxl.erp.itemhistory.page", "/hxl.erp.itemhistory.read", "/hxl.erp.brand.save", "/hxl.erp.brand.center.find", "/hxl.erp.brand.delete", "/hxl.erp.brand.find", "/hxl.erp.brand.update", "/hxl.erp.category.sequence.updateV2", "/hxl.erp.category.delete", "/hxl.erp.categorytemplate.download", "/hxl.erp.category.initlevelId", "/hxl.erp.category.maxlevel.read", "/hxl.erp.category.save", "/hxl.erp.category.update", "/hxl.erp.category.findbyupdatetime", "/hxl.erp.category.findmaxlevel", "/hxl.erp.category.sequence.init", "/hxl.erp.category.import", "/hxl.erp.category.find", "/hxl.erp.settlementcategory.center.find", "/hxl.erp.category.center.find", "/hxl.erp.category.sequence.update", "/hxl.erp.combinationitemtemplate.download", "/hxl.erp.item.summary", "/hxl.erp.makebillitem.import", "/hxl.erp.item.image.import", "/hxl.erp.item.findbyupdatetime", "/hxl.erp.item.app.read", "/hxl.erp.item.code.get", "/hxl.erp.item.import", "/hxl.erp.item.shorttemplate.download", "/hxl.erp.item.new.update", "/hxl.erp.item.supplier.export", "/hxl.erp.item.center.read", "/hxl.erp.item.wms.findId", "/hxl.erp.item.distribute", "/hxl.erp.item.scm.update", "/hxl.erp.itemrecycle.page", "/hxl.erp.item.supplier.priceadjustorder.page", "/hxl.erp.item.findbyids", "/hxl.erp.item.read", "/hxl.erp.item.summary.read", "/hxl.erp.itemsupdatetemplate.download", "/hxl.erp.item.save", "/hxl.erp.item.mustsellalaysis.export", "/hxl.erp.item.file.handle", "/hxl.erp.item.center.find", "/hxl.erp.business.items.batchimport", "/hxl.erp.synchronize.ama", "/hxl.erp.item.mustsellalaysis.find", "/hxl.erp.item.batch.distribute", "/hxl.erp.item.supplier.page", "/hxl.erp.item.card.find", "/hxl.erp.item.wholesaleprice.handle", "/hxl.erp.item.updatedelivery", "/hxl.erp.items.withoutcombination.batchimport", "/hxl.erp.item.image.add", "/hxl.erp.itemproductspec.find", "/hxl.erp.item.batch.update", "/hxl.erp.itembarcode.find", "/hxl.erp.makebillitemtemplate.download", "/hxl.erp.itemrecycle.export", "/hxl.erp.mainspec.import", "/hxl.erp.item.image.read", "/hxl.erp.item.wms.update", "/hxl.erp.item.page", "/hxl.erp.itemtemplate.download", "/hxl.erp.items.eliminate.batchimport", "/hxl.erp.item.findbybarcode", "/hxl.erp.itemrecycle.batchdelete", "/hxl.erp.item.app.page", "/hxl.erp.item.find", "/hxl.erp.combinationitem.import", "/hxl.erp.item.export", "/hxl.erp.item.short.ids.find", "/hxl.erp.item.center.short.page", "/hxl.erp.itembarcode.export", "/hxl.erp.items.batchimport", "/hxl.erp.item.supplier.read", "/hxl.erp.combinationitem.export", "/hxl.erp.item.batch.update.count", "/hxl.erp.itemrecycle.batchrecover", "/hxl.erp.item.short.page", "/hxl.erp.item.eliminate", "/hxl.erp.itemrecycle.batcheliminate", "/hxl.erp.item.updateitemattr", "/hxl.erp.mainspecitem.export", "/hxl.erp.item.summary.initial.save", "/hxl.erp.center.item.read", "/hxl.erp.mainspecitemtemplate.download", "/hxl.erp.item.findbyupdatetimeV2", "/hxl.erp.item.update", "/hxl.erp.item.image.export", "/hxl.erp.item.update.import", "/hxl.erp.dept.delete", "/hxl.erp.dept.save", "/hxl.erp.dept.find", "/hxl.erp.dept.update", "/hxl.erp.itemlabel.batchupdate.tips", "/hxl.erp.itemlabel.import.template", "/hxl.erp.itemlabel.org.level2", "/hxl.erp.itemlabel.ama.upload", "/hxl.erp.itemlabel.read", "/hxl.erp.itemlabel.save", "/hxl.erp.itemlabel.batchdelete", "/hxl.erp.itemlabel.delete", "/hxl.erp.itemlabel.item.import", "/hxl.erp.itemlabel.batchupdate", "/hxl.erp.itemlabel.find", "/hxl.erp.itemlabel.org.all", "/hxl.erp.itemlabel.update", "/hxl.erp.itemlabel.save.bi", "/hxl.erp.itemlabel.item.page", "/hxl.erp.itemlabel.find.org.level1", "/hxl.erp.itemlabel.batchexport", "/hxl.erp.itemlabel.import", "/hxl.erp.itemmanagement.find", "/hxl.erp.itemmanagement.syncama", "/hxl.erp.itemunit.update", "/hxl.erp.itemunit.find", "/hxl.erp.itemunit.delete", "/hxl.erp.itemunit.save", "/hxl.erp.labourhistory.page", "/hxl.erp.labourhistory.read", "/hxl.erp.labourcategory.delete", "/hxl.erp.labourcategory.save", "/hxl.erp.labourcategory.update", "/hxl.erp.labourcategory.find", "/hxl.erp.labour.save", "/hxl.erp.labourname.import", "/hxl.erp.labourtemplate.download", "/hxl.erp.labour.page", "/hxl.erp.labour.import", "/hxl.erp.labour.update", "/hxl.erp.labour.batchupdate", "/hxl.erp.labour.read", "/hxl.erp.labour.export", "/hxl.erp.labour.findbyupdatetime", "/hxl.erp.labour.delete", "/hxl.erp.labournametemplate.download", "/hxl.erp.wmslabourfee.save", "/hxl.erp.server.org.tree", "/hxl.erp.org.noauth.tree", "/hxl.erp.org.update", "/hxl.erp.org.findbyupdatetime", "/hxl.erp.org.save", "/hxl.erp.org.findbylevel", "/hxl.erp.org.read", "/hxl.erp.org.all.find", "/hxl.erp.org.delete", "/hxl.erp.org.noLimit.find", "/hxl.erp.org.batchdelete", "/hxl.erp.server.org.check.default", "/hxl.erp.org.findstorebyorgids", "/hxl.erp.org.page", "/hxl.erp.org.tree", "/hxl.erp.org.find", "/hxl.erp.org.skulimit.excludeitem.export", "/hxl.erp.org.skulimit.excludeitem.save", "/hxl.erp.org.skulimittemplate.download", "/hxl.erp.org.skulimit.excludeitem.find", "/hxl.erp.org.skulimit.excludeitemtemplate.download", "/hxl.erp.org.skulimit.excludeitem.log.find", "/hxl.erp.org.skulimit.update", "/hxl.erp.org.skulimit.log.find", "/hxl.erp.org.skulimit.page", "/hxl.erp.org.skulimit.update.import", "/hxl.erp.org.skulimit.excludeitem.import", "/hxl.erp.org.skulimit.export", "/hxl.erp.org.mainspecitem.export", "/hxl.erp.org.item.update", "/hxl.erp.org.item.findbyupdatetime", "/hxl.erp.org.itembarcode.export", "/hxl.erp.org.item.update.import", "/hxl.erp.org.short.item.page", "/hxl.erp.org.item.app.page", "/hxl.erp.org.item.export", "/hxl.erp.org.business.scope.item.import", "/hxl.erp.org.business.scope.item.add", "/hxl.erp.org.business.scope.item.batchdelete", "/hxl.erp.org.item.app.read", "/hxl.erp.org.business.scope.item.export", "/hxl.erp.org.item.page", "/hxl.erp.org.item.batchimport", "/hxl.erp.org.business.scope.item.page", "/hxl.erp.org.item.batch.update.ids", "/hxl.erp.org.business.scope.item.template.download", "/hxl.erp.org.item.read", "/hxl.erp.org.distinct.item.page", "/hxl.erp.org.item.batch.update.count", "/hxl.erp.org.item.batch.update", "/hxl.erp.org.item.findbyids", "/hxl.erp.org.item.image.export", "/hxl.erp.org.item.batchImporttemplate.download", "/hxl.erp.org.combinationitem.export", "/hxl.erp.org.itemsupdatetemplate.download", "/hxl.erp.baseparam.read", "/hxl.erp.itemtemplate.save", "/hxl.erp.baseparam.save", "/hxl.erp.pos.businessitemid.find", "/hxl.erp.pos.client.page", "/hxl.erp.pos.category.find", "/hxl.erp.pos.paymerchant.read", "/hxl.erp.pos.item.find", "/hxl.erp.posmachine.register", "/hxl.erp.posmachine.pos.delete", "/hxl.erp.posmachine.check", "/hxl.erp.posmachine.delete", "/hxl.erp.posmachine.page", "/hxl.erp.printtemplate.batchdelete", "/hxl.erp.printtemplate.read", "/hxl.erp.printtemplate.save", "/hxl.erp.printtemplate.update", "/hxl.erp.printtemplate.menus", "/hxl.erp.printtemplate.delete", "/hxl.erp.printtemplate.print", "/hxl.erp.printtemplate.find", "/hxl.erp.rolecategory.update", "/hxl.erp.rolecategory.sequence.init", "/hxl.erp.rolecategory.find", "/hxl.erp.rolecategory.delete", "/hxl.erp.rolecategory.save", "/hxl.erp.rolecategory.sequence.updateV2", "/hxl.erp.role.delete", "/hxl.erp.role.check.delete", "/hxl.erp.role.read", "/hxl.erp.rolename.import", "/hxl.erp.role.page", "/hxl.erp.role.batchupdate.overwrite", "/hxl.erp.role.copy", "/hxl.erp.role.save", "/hxl.erp.role.batchupdate.append", "/hxl.erp.role.detail.export", "/hxl.erp.role.hrsinit", "/hxl.erp.role.short.page", "/hxl.erp.role.hrsfind", "/hxl.erp.role.alterlog", "/hxl.erp.role.update", "/hxl.erp.roletemplate.download", "/hxl.erp.rolerange.page", "/hxl.erp.role.batchupdate.delete", "/hxl.erp.roleuser.export", "/hxl.erp.roleuser.page", "/hxl.erp.user.role.transfer", "/hxl.erp.scm.storehouse.findbyids", "/hxl.erp.scm_center.deliveryoutorder.findcount", "/hxl.erp.scm.store.org", "/hxl.erp.scm.supplier.label", "/hxl.erp.scm.item.find", "/hxl.erp.scm.supplierorg.find", "/hxl.erp.scm.supplierid.find", "/hxl.erp.scm.organization.findbyid", "/hxl.erp.storeareacategory.save", "/hxl.erp.storeareacategory.update", "/hxl.erp.storeareacategory.delete", "/hxl.erp.storeareacategory.find", "/hxl.erp.storearea.save", "/hxl.erp.storearea.export", "/hxl.erp.storearea.find", "/hxl.erp.storearea.self.find", "/hxl.erp.storeareatemplate.download", "/hxl.erp.storearea.import", "/hxl.erp.storearea.update", "/hxl.erp.storearea.store.import", "/hxl.erp.storearea.batchupdate", "/hxl.erp.storearea.bms.find", "/hxl.erp.storearea.read", "/hxl.erp.storearea.batch.export", "/hxl.erp.storearea.delete", "/hxl.erp.store.file.pass", "/hxl.erp.store.findbyupdatetimeV2", "/hxl.erp.store.area.find.all", "/hxl.erp.store.read", "/hxl.erp.store.ids.page", "/hxl.erp.store.area.app.find", "/hxl.erp.storecodetemplate.download", "/hxl.erp.store.sharedeliverycenter.find", "/hxl.erp.hrsstore.find", "/hxl.erp.store.level.export", "/hxl.erp.store.allcenter.find", "/hxl.erp.store.ids.find", "/hxl.erp.store.findbyupdatetime", "/hxl.erp.store.balance.read", "/hxl.erp.store.all.deliverycenterstore.find", "/hxl.erp.store.license.waithandle.find", "/hxl.erp.commonstorename.import", "/hxl.erp.kms.store.file.update", "/hxl.erp.store.level.sku.export", "/hxl.erp.store.findmerchant", "/hxl.erp.store.orgstore.find", "/hxl.erp.store.level.sku.find", "/hxl.erp.store.license.update", "/hxl.erp.store.updatemerchantstatus", "/hxl.erp.server.area.find", "/hxl.erp.store.orgcenter.read", "/hxl.erp.store.bms.merchantstatus", "/hxl.erp.store.waithandle.find", "/hxl.erp.store.level.find", "/hxl.erp.store.short.batchimport", "/hxl.erp.storerentV2template.download", "/hxl.erp.store.center.find", "/hxl.erp.store.food.update", "/hxl.erp.store.updatecreditLine", "/hxl.erp.store.oldxlb.sync", "/hxl.erp.store.export", "/hxl.erp.store.weather.live", "/hxl.erp.store.machinecode.check", "/hxl.erp.store.license.handle", "/hxl.erp.store.finance.read", "/hxl.erp.store.orginfo.find", "/hxl.erp.store.taxnoinfo.update", "/hxl.erp.store.import", "/hxl.erp.storename.import", "/hxl.erp.store.dms.bonus", "/hxl.erp.storerent.zip.import", "/hxl.erp.store.short.page", "/hxl.erp.store.update", "/hxl.erp.store.machinecode.intiBitmap", "/hxl.erp.store.file.export", "/hxl.erp.store.all.find", "/hxl.erp.storerent.file.upload", "/hxl.erp.store.file.upload", "/hxl.erp.store.xpayupdate", "/hxl.erp.store.orgdeliverycenter.find", "/hxl.erp.store.count", "/hxl.erp.store.file.refuse", "/hxl.erp.store.sync.amaattr", "/hxl.erp.storesupdatetemplate.download", "/hxl.erp.store.amaaccount.recharge", "/hxl.erp.store.area.detail.export", "/hxl.erp.areastore.find", "/hxl.erp.store.area.find", "/hxl.erp.store.all.shortfind", "/hxl.erp.store.area.detail.page", "/hxl.erp.store.orgstore.read", "/hxl.erp.store.update.import", "/hxl.erp.store.batchupdate", "/hxl.erp.store.usercenterid.synchronize", "/hxl.erp.store.citycode.update", "/hxl.erp.storerent.import", "/hxl.erp.store.customattribute.update", "/hxl.erp.store.cargoownerdelivery.short.page", "/hxl.erp.store.synchronize", "/hxl.erp.store.short.settlement.page", "/hxl.erp.storerent.importV2", "/hxl.erp.store.dms.find", "/hxl.erp.store.shortfind", "/hxl.erp.store.food.waithandle.find", "/hxl.erp.store.area.detail.find", "/hxl.erp.store.short.read", "/hxl.erp.storenametemplate.download", "/hxl.erp.store.page", "/hxl.erp.store.auth.store.find", "/hxl.erp.storefee.page", "/hxl.erp.storefee.read", "/hxl.erp.storefileapply.read", "/hxl.erp.storefileapply.save", "/hxl.erp.storefileapply.pass", "/hxl.erp.storefileapply.update", "/hxl.erp.storefileapply.refuse", "/hxl.erp.storefileapply.waithandle.find", "/hxl.erp.storegroup.import", "/hxl.erp.storegroup.update", "/hxl.erp.storegroup.find", "/hxl.erp.storegroup.save", "/hxl.erp.storegroup.delete", "/hxl.erp.storehardware.brand.save", "/hxl.erp.storehardware.brand.findByCategory", "/hxl.erp.storehardwarebrand.read", "/hxl.erp.storehardwarebrand.export", "/hxl.erp.storehardware.brand.update", "/hxl.erp.storehardware.brand.delete", "/hxl.erp.storehardware.brand.find", "/hxl.erp.storehardware.category.update", "/hxl.erp.storehardware.category.readByName", "/hxl.erp.storehardware.category.read", "/hxl.erp.storehardware.category.delete", "/hxl.erp.storehardware.category.find", "/hxl.erp.storehardware.category.save", "/hxl.erp.storehardware.save", "/hxl.erp.storehardware.update", "/hxl.erp.storehardware.template.download", "/hxl.erp.storehardware.read", "/hxl.erp.storehardware.delete", "/hxl.erp.storehardware.import", "/hxl.erp.storehardware.find", "/hxl.erp.storehardware.upload", "/hxl.erp.storehardware.export", "/hxl.erp.storehouse.pos.find", "/hxl.erp.storehousenametemplate.download", "/hxl.erp.storehouse.update", "/hxl.erp.storehouse.contact.find", "/hxl.erp.storehouse.read", "/hxl.erp.storehouse.centerstoreid.find", "/hxl.erp.storehouse.code.get", "/hxl.erp.storehousename.import", "/hxl.erp.storehouse.scm.read", "/hxl.erp.storehouse.save", "/hxl.erp.storehouse.page", "/hxl.erp.storehouse.delete", "/hxl.erp.storehouse.batchsave", "/hxl.erp.storehouse.store.find", "/hxl.erp.storelabel.find", "/hxl.erp.storelabel.update", "/hxl.erp.storelabel.delete", "/hxl.erp.storelabel.store.find", "/hxl.erp.storelabel.batchdelete", "/hxl.erp.storelabel.batchsave", "/hxl.erp.storelabel.read", "/hxl.erp.storelabel.export", "/hxl.erp.storelabel.save", "/hxl.erp.storelabel.batchexport", "/hxl.erp.storelabel.import", "/hxl.erp.supplierhistory.init", "/hxl.erp.supplierhistory.page", "/hxl.erp.supplierhistory.read", "/hxl.erp.suppliercategory.save", "/hxl.erp.suppliercategory.findwithsupplier", "/hxl.erp.suppliercategory.update", "/hxl.erp.suppliercategory.center.find", "/hxl.erp.suppliercategory.find", "/hxl.erp.suppliercategory.scm.check", "/hxl.erp.suppliercategory.delete", "/hxl.erp.suppliercontract.invalid", "/hxl.erp.suppliercontract.update", "/hxl.erp.suppliercontract.audit", "/hxl.erp.suppliercontract.read", "/hxl.erp.suppliercontract.item.export", "/hxl.erp.suppliercontract.unloadingfee.update", "/hxl.erp.suppliercontract.file.upload", "/hxl.erp.suppliercontract.reaudit", "/hxl.erp.suppliercontract.batchdelete", "/hxl.erp.suppliercontract.page", "/hxl.erp.suppliercontract.save", "/hxl.erp.suppliernametemplate.download", "/hxl.erp.supplier.children.find", "/hxl.erp.supplier.account.handle", "/hxl.erp.supplier.center.association.find", "/hxl.erp.supplier.unloadtype.read", "/hxl.erp.supplier.batch.update", "/hxl.erp.supplier.update", "/hxl.erp.supplier.producerandexecutivestandard.find", "/hxl.erp.supplier.pdf.convert.img", "/hxl.erp.supplier.center.short.page", "/hxl.erp.suppliertemplate.download", "/hxl.erp.supplier.import", "/hxl.erp.scm.supplier.check", "/hxl.erp.supplier.export", "/hxl.erp.supplier.batch.update.cnt", "/hxl.erp.suppliername.import", "/hxl.erp.supplier.delete", "/hxl.erp.supplier.center.read", "/hxl.erp.supplierassociationsupplier.export", "/hxl.erp.supplier.read", "/hxl.erp.supplier.merchantstatus.enable", "/hxl.erp.supplier.producer.find", "/hxl.erp.supplier.code.get", "/hxl.erp.supplier.read.supplier", "/hxl.erp.supplier.sharestore.import", "/hxl.erp.supplier.account.find", "/hxl.erp.supplieruser.copy", "/hxl.erp.supplier.platformmoneymanagement.enable", "/hxl.erp.supplier.short.page", "/hxl.erp.supplier.app.update", "/hxl.erp.supplier.balance.read", "/hxl.erp.supplier.page", "/hxl.erp.supplier.batch.update.count", "/hxl.erp.supplier.save", "/hxl.erp.supplier.getsharestore", "/hxl.erp.parentassociationchild.export", "/hxl.erp.supplier.findbyupdatetime", "/hxl.erp.supplier.auth.check", "/hxl.erp.supplier.file.handle", "/hxl.erp.supplier.sharestoretemplate.download", "/hxl.erp.supplier.children.batchfind", "/hxl.erp.supplier.label.save", "/hxl.erp.supplier.label.export", "/hxl.erp.suppliermainbody.update", "/hxl.erp.suppliermainbody.find", "/hxl.erp.suppliermainbody.delete", "/hxl.erp.suppliermainbody.save", "/hxl.erp.supplierqualityorder.comment.save", "/hxl.erp.supplierqualityorder.submit", "/hxl.erp.supplierqualityorder.page", "/hxl.erp.supplierqualityorder.invalid", "/hxl.erp.supplierqualityorder.param.find", "/hxl.erp.supplierqualityorder.file.upload", "/hxl.erp.supplierqualityorder.pass", "/hxl.erp.supplierqualityorder.save", "/hxl.erp.supplierqualityorder.update", "/hxl.erp.supplierqualityorder.item.page", "/hxl.erp.supplierqualityorder.export", "/hxl.erp.supplierqualityorder.read", "/hxl.erp.supplierqualityorder.deny", "/hxl.erp.userapplyorder.deny", "/hxl.erp.userapplyorder.audit", "/hxl.erp.userapplyorder.delete", "/hxl.erp.userapplyorder.approve", "/hxl.erp.userapplyorder.alterhistory", "/hxl.erp.userapplyorder.read", "/hxl.erp.userapplyorder.update", "/hxl.erp.userapplyorder.refuse", "/hxl.erp.userapplyorder.pass", "/hxl.erp.userapplyorder.saveandaudit", "/hxl.erp.userapplyorder.save", "/hxl.erp.userapplyorder.modify", "/hxl.erp.userapplyorder.softdelete", "/hxl.erp.userapplyorder.page", "/hxl.erp.userapplyrole.role.find", "/hxl.erp.userapplyrole.delete", "/hxl.erp.userapplyrole.userattribute.find", "/hxl.erp.userapplyrole.user.find", "/hxl.erp.userapplyrole.read", "/hxl.erp.userapplyrole.attribute.find", "/hxl.erp.userapplyrole.update", "/hxl.erp.userapplyrole.page", "/hxl.erp.userapplyrole.save", "/hxl.erp.usercolumn.get", "/hxl.erp.usercolumn.update", "/hxl.erp.user.tel.find", "/hxl.erp.userphone.import", "/hxl.erp.user.supplier.batchupdate.count", "/hxl.erp.user.logincode.send", "/hxl.erp.thirdparty.auth", "/hxl.erp.user.account.app.org.find", "/hxl.erp.user.pos.batchupdate.count", "/hxl.erp.user.pos.export", "/hxl.erp.user.miniapp.find", "/hxl.erp.usertemplate.pos.download", "/hxl.erp.user.temp.save", "/hxl.erp.user.notice.status.read", "/hxl.erp.user.client.export", "/hxl.erp.user.switchstore.page", "/hxl.erp.user.supplier.export", "/hxl.erp.user.supplier.batchupdate", "/hxl.erp.user.pos.save", "/hxl.erp.user.alterlog", "/hxl.erp.outeruser.update.import", "/hxl.erp.usertemplate.supplier.download", "/hxl.erp.user.findbynameandaccount", "/hxl.erp.user.pos.import", "/hxl.erp.outeruser.export", "/hxl.erp.user.server.login", "/hxl.erp.user.read", "/hxl.erp.user.batchlogout", "/hxl.erp.user.id.find", "/hxl.erp.user.self.update", "/hxl.erp.user.client.import", "/hxl.erp.user.batchupdate.count", "/hxl.erp.usertemplate.client.download", "/hxl.erp.usernametemplate.download", "/hxl.erp.usersupdatetemplate.client.download", "/hxl.erp.user.account.app.login", "/hxl.erp.user.findByAuthorityIds", "/hxl.erp.user.pos.page", "/hxl.erp.user.client.update", "/hxl.erp.user.supplier.update", "/hxl.erp.user.pos.update.import", "/hxl.erp.user.changepwdtelcode.send", "/hxl.erp.user.client.update.import", "/hxl.erp.user.findbyupdatetime", "/hxl.erp.user.psw.update", "/hxl.erp.user.account.login", "/hxl.erp.user.client.page", "/hxl.erp.user.wechat.bind", "/hxl.erp.user.pos.login", "/hxl.erp.user.changetelcodechenk.send", "/hxl.erp.curruser.wechat.bind", "/hxl.erp.user.supplier.page", "/hxl.erp.user.querysupplierids.find", "/hxl.erp.user.supplier.import", "/hxl.erp.user.ama", "/hxl.erp.user.update", "/hxl.erp.user.store.switch", "/hxl.erp.user.wechat.unbind", "/hxl.erp.user.tel.change", "/hxl.erp.user.addamauser", "/hxl.erp.storeuser.page", "/hxl.erp.user.checkcode.send", "/hxl.erp.user.batchdelete", "/hxl.erp.user.import", "/hxl.erp.user.changetelcode.send", "/hxl.erp.user.pos.update", "/hxl.erp.user.resetcode.send", "/hxl.erp.user.temp.login", "/hxl.erp.user.page", "/hxl.erp.user.findbyaccount", "/hxl.erp.user.app.qrcode.scan", "/hxl.erp.user.resetpwdvoicecode.send", "/hxl.erp.user.pos.batchupdate", "/hxl.erp.user.save", "/hxl.erp.user.weixintoken.refresh", "/hxl.erp.user.export", "/hxl.erp.user.client.find", "/hxl.erp.user.test.wechat.qrcode.get", "/hxl.erp.user.querysupplieridandstoreids.find", "/hxl.erp.userhistory.init", "/hxl.erp.usersupdatetemplate.pos.download", "/hxl.erp.user.wechat.login", "/hxl.erp.user.supplier.save", "/hxl.erp.xlbusertemplate.download", "/hxl.erp.user.outer.page", "/hxl.erp.user.findbytel", "/hxl.erp.user.find", "/hxl.erp.user.tel.read", "/hxl.erp.user.supplier.update.import", "/weixin.mp.oauth2.callback", "/hxl.erp.outeruser.updatetemplate.download", "/hxl.erp.user.logout", "/hxl.erp.user.client.save", "/hxl.erp.user.app.login", "/hxl.erp.username.import", "/hxl.erp.user.changetelcodechenk.confirm", "/hxl.erp.user.pwd.reset", "/hxl.erp.user.token.refresh", "/hxl.erp.user.app.qrcode.verify", "/hxl.erp.xlbuser.import", "/hxl.erp.user.miniapp.logincode.send", "/hxl.erp.user.login", "/hxl.erp.user.app.qrcode.create", "/hxl.erp.user.image.update", "/hxl.erp.user.notice.status.update", "/hxl.erp.user.item.find", "/hxl.erp.userposition.find", "/hxl.erp.outeruser.template.download", "/hxl.erp.user.all.page", "/hxl.erp.user.voicecode.send", "/hxl.erp.outeruser.import", "/hxl.erp.userphonetemplate.download", "/hxl.erp.user.batchupdate", "/hxl.erp.usertemplate.download", "/hxl.erp.user.app.update", "/hxl.erp.user.server.save", "/hxl.erp.user.update.import", "/hxl.erp.user.delete", "/hxl.erp.usersupdatetemplate.supplier.download", "/hxl.erp.userhistory.add", "/hxl.erp.user.resetpwdcode.send", "/hxl.erp.user.wechat.qrcode.get", "/hxl.erp.usersupdatetemplate.download", "/hxl.erp.user.resetpwd.confirm", "/hxl.erp.userdept.update", "/hxl.erp.userdept.delete", "/hxl.erp.userdept.find", "/hxl.erp.userdept.save", "/hxl.erp.userlocation.batchsave", "/hxl.erp.userlocation.save", "/hxl.erp.userlocation.getlatest", "/hxl.erp.userlog.printscreen.save", "/hxl.erp.userlog.export", "/hxl.erp.userlog.page", "/hxl.erp.user.store.storagecategory.find", "/hxl.erp.user.store.storagecategory.export", "/starter.progress.interrupt", "/starter.progress.create", "/starter.progress.query", "/hxl.erp.account.user.find", "/hxl.erp.account.codelogin.user.find", "/hxl.erp.user.resetpwd.find", "/hxl.erp.account.captchaverify", "/hxl.erp.account.pwd.user.find", "/hxl.erp.account.wechat.login", "/hxl.erp.app.weather.find", "/hxl.erp.metadata.saveorupdate", "/hxl.erp.metadata.queryModelViewMetaData", "/idaas.erp.user.profile", "/hxl.erp.store.short.code.find", "/hxl.erp.user.bind.customerservice", "/hxl.erp.service.account.page", "/hxl.erp.service.account.grant", "/hxl.erp.service.account.terminate", "/hxl.erp.user.tel.confirm", "/hxl.erp.user.app.qrcode.query", "/hxl.erp.role.authority.find", "/hxl.erp.external.platform.store.log.export", "/erp.metadata.queryCustomModelViewMetaData", "/erp.metadata.queryModelViewMetaData", "/hxl.erp.storehistory.read", "/hxl.erp.store.external.platform.store.create.retry", "/hxl.erp.storerelocation.read", "/hxl.erp.storemodification.find", "/hxl.erp.supplier.organization.file.upload", "/hxl.erp.supplier.organization.apply.query", "/hxl.erp.supplier.organization.approve.create", "/hxl.erp.supplier.organization.order.query", "/hxl.erp.org.skulimit.read", "/hxl.erp.org.tree.invoice"]