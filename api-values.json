["/erp-mdm/hxl.erp.app.calendar.find", "/erp-mdm/hxl.erp.app.forecastweather.find", "/erp-mdm/hxl.erp.iauth.update", "/erp-mdm/hxl.erp.authority.find", "/erp-mdm/hxl.erp.roleauthority.export", "/erp-mdm/hxl.erp.authority.search", "/erp-mdm/hxl.erp.bms.merchant.find", "/erp-mdm/hxl.erp.bms.merchant.update", "/erp-mdm/hxl.erp.businessarea.updatename", "/erp-mdm/hxl.erp.businessarea.read", "/erp-mdm/hxl.erp.businessarea.store.detail.find", "/erp-mdm/hxl.erp.businessarea.detail.find", "/erp-mdm/hxl.erp.businessarea.store.import", "/erp-mdm/hxl.erp.businessarea.store.export", "/erp-mdm/hxl.erp.businessarea.export", "/erp-mdm/hxl.erp.businessarea.store.find", "/erp-mdm/hxl.erp.businessarea.update", "/erp-mdm/hxl.erp.businessarea.readbyareacode", "/erp-mdm/hxl.erp.businessarea.import", "/erp-mdm/hxl.erp.businessarea.find", "/erp-mdm/hxl.erp.businessareastoretemplate.download", "/erp-mdm/hxl.erp.businessarea.hrs.find", "/erp-mdm/hxl.erp.businessarea.child.find", "/erp-mdm/hxl.erp.businessarea.template.download", "/erp-mdm/hxl.erp.businessarea.save", "/erp-mdm/hxl.erp.businessarea.delete", "/erp-mdm/hxl.erp.businessarea.short.read", "/erp-mdm/hxl.erp.businessscopecategory.update", "/erp-mdm/hxl.erp.businessscopecategory.save", "/erp-mdm/hxl.erp.businessscopecategory.find", "/erp-mdm/hxl.erp.businessscopecategory.delete", "/erp-mdm/hxl.erp.businessscope.batchdelete", "/erp-mdm/hxl.erp.businessscope.store.find", "/erp-mdm/hxl.erp.businessscope.heart.check", "/erp-mdm/hxl.erp.businessscope.reuploadama", "/erp-mdm/hxl.erp.businessscope.heart.beat", "/erp-mdm/hxl.erp.businessscope.initsysdata.update", "/erp-mdm/hxl.erp.businessscope.read", "/erp-mdm/hxl.erp.businessscope.syncama", "/erp-mdm/hxl.erp.businessscope.short.find", "/erp-mdm/hxl.erp.businessscope.batchupdatev2", "/erp-mdm/hxl.erp.businessscope.item.export", "/erp-mdm/hxl.erp.businessscope.org.check", "/erp-mdm/hxl.erp.businessscope.update", "/erp-mdm/hxl.erp.businessscope.heart.delete", "/erp-mdm/hxl.erp.businessscope.item.download", "/erp-mdm/hxl.erp.businessscope.itemabc.update", "/erp-mdm/hxl.erp.businessscope.item.import", "/erp-mdm/hxl.erp.businessscope.find", "/erp-mdm/hxl.erp.businessscope.item.save", "/erp-mdm/hxl.erp.businessscope.delete", "/erp-mdm/hxl.erp.businessscope.uploadama", "/erp-mdm/hxl.erp.businessscope.item.page", "/erp-mdm/hxl.erp.businessscope.store.item.batchexport", "/erp-mdm/hxl.erp.businessscope.syncamaone", "/erp-mdm/hxl.erp.businessscope.store.item.page", "/erp-mdm/hxl.erp.businessscope.batchupdate", "/erp-mdm/hxl.erp.businessscope.store.download", "/erp-mdm/hxl.erp.businessscope.item.delete", "/erp-mdm/hxl.erp.businessscope.save", "/erp-mdm/hxl.erp.businessscope.batchexport", "/erp-mdm/hxl.erp.business.scope.backup.init", "/erp-mdm/hxl.erp.business.scope.backup.batchexport", "/erp-mdm/hxl.erp.business.scope.backup.read", "/erp-mdm/hxl.erp.business.scope.backup.back", "/erp-mdm/hxl.erp.business.scope.backup.page", "/erp-mdm/hxl.erp.cargo.owner.conf.relation.pageforwms", "/erp-mdm/hxl.erp.cargo.owner.import", "/erp-mdm/hxl.erp.cargo.owner.log.page", "/erp-mdm/hxl.erp.cargo.owner.save", "/erp-mdm/hxl.erp.cargo.owner.template.download", "/erp-mdm/hxl.erp.cargo.owner.page", "/erp-mdm/hxl.erp.cargo.owner.enabled", "/erp-mdm/hxl.erp.cargo.owner.disabled", "/erp-mdm/hxl.cargo.owner.orgid.find", "/erp-mdm/hxl.erp.cargo.owner.pageforwms", "/erp-mdm/hxl.scm.cargo.orgid.find", "/erp-mdm/hxl.erp.cargo.owner.export", "/erp-mdm/hxl.erp.cargo.owner.findOrgCode", "/erp-mdm/hxl.erp.cargo.owner.pageforinner", "/erp-mdm/hxl.erp.cargo.owner.batchdelete", "/erp-mdm/hxl.erp.center.association.itembrand.find", "/erp-mdm/hxl.erp.center.itemcategory.find", "/erp-mdm/hxl.erp.center.abnormalcategory.find", "/erp-mdm/hxl.erp.center.qualityreportposorder.itemdetail", "/erp-mdm/hxl.erp.center.itembrand.read", "/erp-mdm/hxl.erp.center.supplier.distributecheck", "/erp-mdm/hxl.erp.center.org.find", "/erp-mdm/hxl.erp.center.item.centerid.update", "/erp-mdm/hxl.erp.checksupplier.page", "/erp-mdm/hxl.erp.checksupplier.save", "/erp-mdm/hxl.erp.checksupplier.check", "/erp-mdm/hxl.erp.checksupplier.delete", "/erp-mdm/hxl.erp.checksupplier.read", "/erp-mdm/hxl.erp.checksupplier.update", "/erp-mdm/hxl.erp.checksupplier.download", "/erp-mdm/hxl.erp.checksupplier.import", "/erp-mdm/hxl.erp.clienthistory.page", "/erp-mdm/hxl.erp.clienthistory.read", "/erp-mdm/hxl.erp.clientcategory.update", "/erp-mdm/hxl.erp.clientcategory.find", "/erp-mdm/hxl.erp.clientcategory.save", "/erp-mdm/hxl.erp.clientcategory.delete", "/erp-mdm/hxl.erp.clientcategory.findwithclient", "/erp-mdm/hxl.erp.client.read", "/erp-mdm/hxl.erp.client.updatecreditLine", "/erp-mdm/hxl.erp.clienttemplate.download", "/erp-mdm/hxl.erp.clientupdatetemplate.download", "/erp-mdm/hxl.erp.client.findbyupdatetime", "/erp-mdm/hxl.erp.client.export", "/erp-mdm/hxl.erp.client.balance.read", "/erp-mdm/hxl.erp.client.import", "/erp-mdm/hxl.erp.clientnametemplate.download", "/erp-mdm/hxl.erp.client.update", "/erp-mdm/hxl.erp.client.storerelationship", "/erp-mdm/hxl.erp.client.code.get", "/erp-mdm/hxl.erp.client.update.import", "/erp-mdm/hxl.erp.client.batchupdate.count", "/erp-mdm/hxl.erp.client.bms.merchantstatus", "/erp-mdm/hxl.erp.clientname.import", "/erp-mdm/hxl.erp.client.delete", "/erp-mdm/hxl.erp.client.batchupdate", "/erp-mdm/hxl.erp.client.save", "/erp-mdm/hxl.erp.client.page", "/erp-mdm/hxl.erp.company.token.refresh", "/erp-mdm/hxl.erp.company.token.get", "/erp-mdm/hxl.erp.company.refreshtoken.get", "/erp-mdm/hxl.erp.company.init", "/erp-mdm/hxl.erp.company.find", "/erp-mdm/hxl.erp.company.account.find", "/erp-mdm/hxl.erp.companyinvoice.page", "/erp-mdm/hxl.erp.companyinvoice.find", "/erp-mdm/hxl.erp.companyinvoice.account.find", "/erp-mdm/hxl.erp.companyinvoice.save", "/erp-mdm/hxl.erp.companyinvoice.delete", "/erp-mdm/hxl.erp.companyinvoice.export", "/erp-mdm/hxl.erp.companyinvoice.read", "/erp-mdm/hxl.erp.contract.page", "/erp-mdm/hxl.erp.contract.whitelist.create", "/erp-mdm/hxl.erp.contract.whitelist.export", "/erp-mdm/hxl.erp.contract.enum", "/erp-mdm/hxl.erp.contract.whitelist.page", "/erp-mdm/hxl.erp.sign.contract.job", "/erp-mdm/hxl.erp.contract.read", "/erp-mdm/hxl.erp.contract.triggertask", "/erp-mdm/hxl.erp.sign.contract.callback", "/erp-mdm/hxl.erp.contract.download", "/erp-mdm/hxl.erp.contract.view", "/erp-mdm/hxl.erp.contract.create", "/erp-mdm/hxl.erp.sign.certification", "/erp-mdm/hxl.erp.sign.certification.check", "/erp-mdm/hxl.erp.contract.list", "/erp-mdm/hxl.erp.contract.template.check", "/erp-mdm/hxl.erp.contract.template.read", "/erp-mdm/hxl.erp.contract.template.update", "/erp-mdm/hxl.erp.contract.template.acquire", "/erp-mdm/hxl.erp.contract.template.delete", "/erp-mdm/hxl.erp.contract.template.page", "/erp-mdm/hxl.erp.contract.template.save", "/erp-mdm/hxl.erp.customattribute.save", "/erp-mdm/hxl.erp.customattribute.delete", "/erp-mdm/hxl.erp.customattribute.sequence.update", "/erp-mdm/hxl.erp.customattributename.check.find", "/erp-mdm/hxl.erp.customattribute.update", "/erp-mdm/hxl.erp.customattribute.initial", "/erp-mdm/hxl.erp.customattribute.show.find", "/erp-mdm/hxl.erp.customattribute.find", "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.batchdelete", "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.save", "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.readbystoreIds", "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.import", "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.export", "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.sync", "/erp-mdm/hxl.erp.delivery.cargo.owner.find", "/erp-mdm/hxl.erp.delivery.cargo.owner.org.find", "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.page", "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.copy", "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.wholesale", "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.template.download", "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.log.page", "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.pageforwms", "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.update", "/erp-mdm/hxl.erp.file.compress", "/erp-mdm/hxl.erp.file.find", "/erp-mdm/hxl.erp.file.ocr.handwriting", "/erp-mdm/hxl.erp.file.businesslicensecheck", "/erp-mdm/hxl.erp.supplierpriceadjust.file.upload", "/erp-mdm/hxl.erp.file.delete", "/erp-mdm/hxl.erp.file.ocr", "/erp-mdm/hxl.erp.file.ocr.sign", "/erp-mdm/hxl.erp.file.upload", "/erp-mdm/hxl.erp.file.ocr.ignore", "/erp-mdm/hxl.erp.file.checkexist", "/erp-mdm/hxl.erp.font.upload", "/erp-mdm/hxl.erp.font.file.upload", "/erp-mdm/hxl.erp.font.find", "/erp-mdm/hxl.erp.fsms.userAuthority.find", "/erp-mdm/hxl.erp.hardwarecategory.delete", "/erp-mdm/hxl.erp.hardwarecategory.update", "/erp-mdm/hxl.erp.hardwarecategory.find", "/erp-mdm/hxl.erp.hardwarecategory.save", "/erp-mdm/hxl.erp.hardware.find", "/erp-mdm/hxl.erp.hardware.delete", "/erp-mdm/hxl.erp.hardware.save", "/erp-mdm/hxl.erp.hardware.update", "/erp-mdm/hxl.erp.iauth.generateoneuserstore", "/erp-mdm/hxl.erp.iauth.generatealluserstore", "/erp-mdm/hxl.erp.iauth.generatepartuserstore", "/erp-mdm/hxl.erp.investmentorder.import", "/erp-mdm/hxl.erp.investmentorder.update", "/erp-mdm/hxl.erp.investmentorder.audit", "/erp-mdm/hxl.erp.investmentorder.save", "/erp-mdm/hxl.erp.investmentorder.read", "/erp-mdm/hxl.erp.investmentorder.batchdelete", "/erp-mdm/hxl.erp.investmentorder.page", "/erp-mdm/hxl.erp.investmentorder.download", "/erp-mdm/hxl.erp.investmentorder.invalid", "/erp-mdm/hxl.erp.investmentsubjectcategory.delete", "/erp-mdm/hxl.erp.investmentsubjectcategory.init", "/erp-mdm/hxl.erp.investmentsubjectcategory.save", "/erp-mdm/hxl.erp.investmentsubjectcategory.update", "/erp-mdm/hxl.erp.investmentsubjectcategory.find", "/erp-mdm/hxl.erp.investmentsubject.init", "/erp-mdm/hxl.erp.investmentsubject.update", "/erp-mdm/hxl.erp.investmentsubject.find", "/erp-mdm/hxl.erp.investmentsubject.save", "/erp-mdm/hxl.erp.investmentsubject.delete", "/erp-mdm/hxl.erp.itemhistory.page", "/erp-mdm/hxl.erp.itemhistory.read", "/erp-mdm/hxl.erp.brand.save", "/erp-mdm/hxl.erp.brand.center.find", "/erp-mdm/hxl.erp.brand.delete", "/erp-mdm/hxl.erp.brand.find", "/erp-mdm/hxl.erp.brand.update", "/erp-mdm/hxl.erp.category.sequence.updateV2", "/erp-mdm/hxl.erp.category.delete", "/erp-mdm/hxl.erp.categorytemplate.download", "/erp-mdm/hxl.erp.category.initlevelId", "/erp-mdm/hxl.erp.category.maxlevel.read", "/erp-mdm/hxl.erp.category.save", "/erp-mdm/hxl.erp.category.update", "/erp-mdm/hxl.erp.category.findbyupdatetime", "/erp-mdm/hxl.erp.category.findmaxlevel", "/erp-mdm/hxl.erp.category.sequence.init", "/erp-mdm/hxl.erp.category.import", "/erp-mdm/hxl.erp.category.find", "/erp-mdm/hxl.erp.settlementcategory.center.find", "/erp-mdm/hxl.erp.category.center.find", "/erp-mdm/hxl.erp.category.sequence.update", "/erp-mdm/hxl.erp.combinationitemtemplate.download", "/erp-mdm/hxl.erp.item.summary", "/erp-mdm/hxl.erp.makebillitem.import", "/erp-mdm/hxl.erp.item.image.import", "/erp-mdm/hxl.erp.item.findbyupdatetime", "/erp-mdm/hxl.erp.item.app.read", "/erp-mdm/hxl.erp.item.code.get", "/erp-mdm/hxl.erp.item.import", "/erp-mdm/hxl.erp.item.shorttemplate.download", "/erp-mdm/hxl.erp.item.new.update", "/erp-mdm/hxl.erp.item.supplier.export", "/erp-mdm/hxl.erp.item.center.read", "/erp-mdm/hxl.erp.item.wms.findId", "/erp-mdm/hxl.erp.item.distribute", "/erp-mdm/hxl.erp.item.scm.update", "/erp-mdm/hxl.erp.itemrecycle.page", "/erp-mdm/hxl.erp.item.supplier.priceadjustorder.page", "/erp-mdm/hxl.erp.item.findbyids", "/erp-mdm/hxl.erp.item.read", "/erp-mdm/hxl.erp.item.summary.read", "/erp-mdm/hxl.erp.itemsupdatetemplate.download", "/erp-mdm/hxl.erp.item.save", "/erp-mdm/hxl.erp.item.mustsellalaysis.export", "/erp-mdm/hxl.erp.item.file.handle", "/erp-mdm/hxl.erp.item.center.find", "/erp-mdm/hxl.erp.business.items.batchimport", "/erp-mdm/hxl.erp.synchronize.ama", "/erp-mdm/hxl.erp.item.mustsellalaysis.find", "/erp-mdm/hxl.erp.item.batch.distribute", "/erp-mdm/hxl.erp.item.supplier.page", "/erp-mdm/hxl.erp.item.card.find", "/erp-mdm/hxl.erp.item.wholesaleprice.handle", "/erp-mdm/hxl.erp.item.updatedelivery", "/erp-mdm/hxl.erp.items.withoutcombination.batchimport", "/erp-mdm/hxl.erp.item.image.add", "/erp-mdm/hxl.erp.itemproductspec.find", "/erp-mdm/hxl.erp.item.batch.update", "/erp-mdm/hxl.erp.itembarcode.find", "/erp-mdm/hxl.erp.makebillitemtemplate.download", "/erp-mdm/hxl.erp.itemrecycle.export", "/erp-mdm/hxl.erp.mainspec.import", "/erp-mdm/hxl.erp.item.image.read", "/erp-mdm/hxl.erp.item.wms.update", "/erp-mdm/hxl.erp.item.page", "/erp-mdm/hxl.erp.itemtemplate.download", "/erp-mdm/hxl.erp.items.eliminate.batchimport", "/erp-mdm/hxl.erp.item.findbybarcode", "/erp-mdm/hxl.erp.itemrecycle.batchdelete", "/erp-mdm/hxl.erp.item.app.page", "/erp-mdm/hxl.erp.item.find", "/erp-mdm/hxl.erp.combinationitem.import", "/erp-mdm/hxl.erp.item.export", "/erp-mdm/hxl.erp.item.short.ids.find", "/erp-mdm/hxl.erp.item.center.short.page", "/erp-mdm/hxl.erp.itembarcode.export", "/erp-mdm/hxl.erp.items.batchimport", "/erp-mdm/hxl.erp.item.supplier.read", "/erp-mdm/hxl.erp.combinationitem.export", "/erp-mdm/hxl.erp.item.batch.update.count", "/erp-mdm/hxl.erp.itemrecycle.batchrecover", "/erp-mdm/hxl.erp.item.short.page", "/erp-mdm/hxl.erp.item.eliminate", "/erp-mdm/hxl.erp.itemrecycle.batcheliminate", "/erp-mdm/hxl.erp.item.updateitemattr", "/erp-mdm/hxl.erp.mainspecitem.export", "/erp-mdm/hxl.erp.item.summary.initial.save", "/erp-mdm/hxl.erp.center.item.read", "/erp-mdm/hxl.erp.mainspecitemtemplate.download", "/erp-mdm/hxl.erp.item.findbyupdatetimeV2", "/erp-mdm/hxl.erp.item.update", "/erp-mdm/hxl.erp.item.image.export", "/erp-mdm/hxl.erp.item.update.import", "/erp-mdm/hxl.erp.dept.delete", "/erp-mdm/hxl.erp.dept.save", "/erp-mdm/hxl.erp.dept.find", "/erp-mdm/hxl.erp.dept.update", "/erp-mdm/hxl.erp.itemlabel.batchupdate.tips", "/erp-mdm/hxl.erp.itemlabel.import.template", "/erp-mdm/hxl.erp.itemlabel.org.level2", "/erp-mdm/hxl.erp.itemlabel.ama.upload", "/erp-mdm/hxl.erp.itemlabel.read", "/erp-mdm/hxl.erp.itemlabel.save", "/erp-mdm/hxl.erp.itemlabel.batchdelete", "/erp-mdm/hxl.erp.itemlabel.delete", "/erp-mdm/hxl.erp.itemlabel.item.import", "/erp-mdm/hxl.erp.itemlabel.batchupdate", "/erp-mdm/hxl.erp.itemlabel.find", "/erp-mdm/hxl.erp.itemlabel.org.all", "/erp-mdm/hxl.erp.itemlabel.update", "/erp-mdm/hxl.erp.itemlabel.save.bi", "/erp-mdm/hxl.erp.itemlabel.item.page", "/erp-mdm/hxl.erp.itemlabel.find.org.level1", "/erp-mdm/hxl.erp.itemlabel.batchexport", "/erp-mdm/hxl.erp.itemlabel.import", "/erp-mdm/hxl.erp.itemmanagement.find", "/erp-mdm/hxl.erp.itemmanagement.syncama", "/erp-mdm/hxl.erp.itemunit.update", "/erp-mdm/hxl.erp.itemunit.find", "/erp-mdm/hxl.erp.itemunit.delete", "/erp-mdm/hxl.erp.itemunit.save", "/erp-mdm/hxl.erp.labourhistory.page", "/erp-mdm/hxl.erp.labourhistory.read", "/erp-mdm/hxl.erp.labourcategory.delete", "/erp-mdm/hxl.erp.labourcategory.save", "/erp-mdm/hxl.erp.labourcategory.update", "/erp-mdm/hxl.erp.labourcategory.find", "/erp-mdm/hxl.erp.labour.save", "/erp-mdm/hxl.erp.labourname.import", "/erp-mdm/hxl.erp.labourtemplate.download", "/erp-mdm/hxl.erp.labour.page", "/erp-mdm/hxl.erp.labour.import", "/erp-mdm/hxl.erp.labour.update", "/erp-mdm/hxl.erp.labour.batchupdate", "/erp-mdm/hxl.erp.labour.read", "/erp-mdm/hxl.erp.labour.export", "/erp-mdm/hxl.erp.labour.findbyupdatetime", "/erp-mdm/hxl.erp.labour.delete", "/erp-mdm/hxl.erp.labournametemplate.download", "/erp-mdm/hxl.erp.wmslabourfee.save", "/erp-mdm/hxl.erp.server.org.tree", "/erp-mdm/hxl.erp.org.noauth.tree", "/erp-mdm/hxl.erp.org.update", "/erp-mdm/hxl.erp.org.findbyupdatetime", "/erp-mdm/hxl.erp.org.save", "/erp-mdm/hxl.erp.org.findbylevel", "/erp-mdm/hxl.erp.org.read", "/erp-mdm/hxl.erp.org.all.find", "/erp-mdm/hxl.erp.org.delete", "/erp-mdm/hxl.erp.org.noLimit.find", "/erp-mdm/hxl.erp.org.batchdelete", "/erp-mdm/hxl.erp.server.org.check.default", "/erp-mdm/hxl.erp.org.findstorebyorgids", "/erp-mdm/hxl.erp.org.page", "/erp-mdm/hxl.erp.org.tree", "/erp-mdm/hxl.erp.org.find", "/erp-mdm/hxl.erp.org.skulimit.excludeitem.export", "/erp-mdm/hxl.erp.org.skulimit.excludeitem.save", "/erp-mdm/hxl.erp.org.skulimittemplate.download", "/erp-mdm/hxl.erp.org.skulimit.excludeitem.find", "/erp-mdm/hxl.erp.org.skulimit.excludeitemtemplate.download", "/erp-mdm/hxl.erp.org.skulimit.excludeitem.log.find", "/erp-mdm/hxl.erp.org.skulimit.update", "/erp-mdm/hxl.erp.org.skulimit.log.find", "/erp-mdm/hxl.erp.org.skulimit.page", "/erp-mdm/hxl.erp.org.skulimit.update.import", "/erp-mdm/hxl.erp.org.skulimit.excludeitem.import", "/erp-mdm/hxl.erp.org.skulimit.export", "/erp-mdm/hxl.erp.org.mainspecitem.export", "/erp-mdm/hxl.erp.org.item.update", "/erp-mdm/hxl.erp.org.item.findbyupdatetime", "/erp-mdm/hxl.erp.org.itembarcode.export", "/erp-mdm/hxl.erp.org.item.update.import", "/erp-mdm/hxl.erp.org.short.item.page", "/erp-mdm/hxl.erp.org.item.app.page", "/erp-mdm/hxl.erp.org.item.export", "/erp-mdm/hxl.erp.org.business.scope.item.import", "/erp-mdm/hxl.erp.org.business.scope.item.add", "/erp-mdm/hxl.erp.org.business.scope.item.batchdelete", "/erp-mdm/hxl.erp.org.item.app.read", "/erp-mdm/hxl.erp.org.business.scope.item.export", "/erp-mdm/hxl.erp.org.item.page", "/erp-mdm/hxl.erp.org.item.batchimport", "/erp-mdm/hxl.erp.org.business.scope.item.page", "/erp-mdm/hxl.erp.org.item.batch.update.ids", "/erp-mdm/hxl.erp.org.business.scope.item.template.download", "/erp-mdm/hxl.erp.org.item.read", "/erp-mdm/hxl.erp.org.distinct.item.page", "/erp-mdm/hxl.erp.org.item.batch.update.count", "/erp-mdm/hxl.erp.org.item.batch.update", "/erp-mdm/hxl.erp.org.item.findbyids", "/erp-mdm/hxl.erp.org.item.image.export", "/erp-mdm/hxl.erp.org.item.batchImporttemplate.download", "/erp-mdm/hxl.erp.org.combinationitem.export", "/erp-mdm/hxl.erp.org.itemsupdatetemplate.download", "/erp-mdm/hxl.erp.baseparam.read", "/erp-mdm/hxl.erp.itemtemplate.save", "/erp-mdm/hxl.erp.baseparam.save", "/erp-mdm/hxl.erp.pos.businessitemid.find", "/erp-mdm/hxl.erp.pos.client.page", "/erp-mdm/hxl.erp.pos.category.find", "/erp-mdm/hxl.erp.pos.paymerchant.read", "/erp-mdm/hxl.erp.pos.item.find", "/erp-mdm/hxl.erp.posmachine.register", "/erp-mdm/hxl.erp.posmachine.pos.delete", "/erp-mdm/hxl.erp.posmachine.check", "/erp-mdm/hxl.erp.posmachine.delete", "/erp-mdm/hxl.erp.posmachine.page", "/erp-mdm/hxl.erp.printtemplate.batchdelete", "/erp-mdm/hxl.erp.printtemplate.read", "/erp-mdm/hxl.erp.printtemplate.save", "/erp-mdm/hxl.erp.printtemplate.update", "/erp-mdm/hxl.erp.printtemplate.menus", "/erp-mdm/hxl.erp.printtemplate.delete", "/erp-mdm/hxl.erp.printtemplate.print", "/erp-mdm/hxl.erp.printtemplate.find", "/erp-mdm/hxl.erp.rolecategory.update", "/erp-mdm/hxl.erp.rolecategory.sequence.init", "/erp-mdm/hxl.erp.rolecategory.find", "/erp-mdm/hxl.erp.rolecategory.delete", "/erp-mdm/hxl.erp.rolecategory.save", "/erp-mdm/hxl.erp.rolecategory.sequence.updateV2", "/erp-mdm/hxl.erp.role.delete", "/erp-mdm/hxl.erp.role.check.delete", "/erp-mdm/hxl.erp.role.read", "/erp-mdm/hxl.erp.rolename.import", "/erp-mdm/hxl.erp.role.page", "/erp-mdm/hxl.erp.role.batchupdate.overwrite", "/erp-mdm/hxl.erp.role.copy", "/erp-mdm/hxl.erp.role.save", "/erp-mdm/hxl.erp.role.batchupdate.append", "/erp-mdm/hxl.erp.role.detail.export", "/erp-mdm/hxl.erp.role.hrsinit", "/erp-mdm/hxl.erp.role.short.page", "/erp-mdm/hxl.erp.role.hrsfind", "/erp-mdm/hxl.erp.role.alterlog", "/erp-mdm/hxl.erp.role.update", "/erp-mdm/hxl.erp.roletemplate.download", "/erp-mdm/hxl.erp.rolerange.page", "/erp-mdm/hxl.erp.role.batchupdate.delete", "/erp-mdm/hxl.erp.roleuser.export", "/erp-mdm/hxl.erp.roleuser.page", "/erp-mdm/hxl.erp.user.role.transfer", "/erp-mdm/hxl.erp.scm.storehouse.findbyids", "/erp-mdm/hxl.erp.scm_center.deliveryoutorder.findcount", "/erp-mdm/hxl.erp.scm.store.org", "/erp-mdm/hxl.erp.scm.supplier.label", "/erp-mdm/hxl.erp.scm.item.find", "/erp-mdm/hxl.erp.scm.supplierorg.find", "/erp-mdm/hxl.erp.scm.supplierid.find", "/erp-mdm/hxl.erp.scm.organization.findbyid", "/erp-mdm/hxl.erp.storeareacategory.save", "/erp-mdm/hxl.erp.storeareacategory.update", "/erp-mdm/hxl.erp.storeareacategory.delete", "/erp-mdm/hxl.erp.storeareacategory.find", "/erp-mdm/hxl.erp.storearea.save", "/erp-mdm/hxl.erp.storearea.export", "/erp-mdm/hxl.erp.storearea.find", "/erp-mdm/hxl.erp.storearea.self.find", "/erp-mdm/hxl.erp.storeareatemplate.download", "/erp-mdm/hxl.erp.storearea.import", "/erp-mdm/hxl.erp.storearea.update", "/erp-mdm/hxl.erp.storearea.store.import", "/erp-mdm/hxl.erp.storearea.batchupdate", "/erp-mdm/hxl.erp.storearea.bms.find", "/erp-mdm/hxl.erp.storearea.read", "/erp-mdm/hxl.erp.storearea.batch.export", "/erp-mdm/hxl.erp.storearea.delete", "/erp-mdm/hxl.erp.store.file.pass", "/erp-mdm/hxl.erp.store.findbyupdatetimeV2", "/erp-mdm/hxl.erp.store.area.find.all", "/erp-mdm/hxl.erp.store.read", "/erp-mdm/hxl.erp.store.ids.page", "/erp-mdm/hxl.erp.store.area.app.find", "/erp-mdm/hxl.erp.storecodetemplate.download", "/erp-mdm/hxl.erp.store.sharedeliverycenter.find", "/erp-mdm/hxl.erp.hrsstore.find", "/erp-mdm/hxl.erp.store.level.export", "/erp-mdm/hxl.erp.store.allcenter.find", "/erp-mdm/hxl.erp.store.ids.find", "/erp-mdm/hxl.erp.store.findbyupdatetime", "/erp-mdm/hxl.erp.store.balance.read", "/erp-mdm/hxl.erp.store.all.deliverycenterstore.find", "/erp-mdm/hxl.erp.store.license.waithandle.find", "/erp-mdm/hxl.erp.commonstorename.import", "/erp-mdm/hxl.erp.kms.store.file.update", "/erp-mdm/hxl.erp.store.level.sku.export", "/erp-mdm/hxl.erp.store.findmerchant", "/erp-mdm/hxl.erp.store.orgstore.find", "/erp-mdm/hxl.erp.store.level.sku.find", "/erp-mdm/hxl.erp.store.license.update", "/erp-mdm/hxl.erp.store.updatemerchantstatus", "/erp-mdm/hxl.erp.server.area.find", "/erp-mdm/hxl.erp.store.orgcenter.read", "/erp-mdm/hxl.erp.store.bms.merchantstatus", "/erp-mdm/hxl.erp.store.waithandle.find", "/erp-mdm/hxl.erp.store.level.find", "/erp-mdm/hxl.erp.store.short.batchimport", "/erp-mdm/hxl.erp.storerentV2template.download", "/erp-mdm/hxl.erp.store.center.find", "/erp-mdm/hxl.erp.store.food.update", "/erp-mdm/hxl.erp.store.updatecreditLine", "/erp-mdm/hxl.erp.store.oldxlb.sync", "/erp-mdm/hxl.erp.store.export", "/erp-mdm/hxl.erp.store.weather.live", "/erp-mdm/hxl.erp.store.machinecode.check", "/erp-mdm/hxl.erp.store.license.handle", "/erp-mdm/hxl.erp.store.finance.read", "/erp-mdm/hxl.erp.store.orginfo.find", "/erp-mdm/hxl.erp.store.taxnoinfo.update", "/erp-mdm/hxl.erp.store.import", "/erp-mdm/hxl.erp.storename.import", "/erp-mdm/hxl.erp.store.dms.bonus", "/erp-mdm/hxl.erp.storerent.zip.import", "/erp-mdm/hxl.erp.store.short.page", "/erp-mdm/hxl.erp.store.update", "/erp-mdm/hxl.erp.store.machinecode.intiBitmap", "/erp-mdm/hxl.erp.store.file.export", "/erp-mdm/hxl.erp.store.all.find", "/erp-mdm/hxl.erp.storerent.file.upload", "/erp-mdm/hxl.erp.store.file.upload", "/erp-mdm/hxl.erp.store.xpayupdate", "/erp-mdm/hxl.erp.store.orgdeliverycenter.find", "/erp-mdm/hxl.erp.store.count", "/erp-mdm/hxl.erp.store.file.refuse", "/erp-mdm/hxl.erp.store.sync.amaattr", "/erp-mdm/hxl.erp.storesupdatetemplate.download", "/erp-mdm/hxl.erp.store.amaaccount.recharge", "/erp-mdm/hxl.erp.store.area.detail.export", "/erp-mdm/hxl.erp.areastore.find", "/erp-mdm/hxl.erp.store.area.find", "/erp-mdm/hxl.erp.store.all.shortfind", "/erp-mdm/hxl.erp.store.area.detail.page", "/erp-mdm/hxl.erp.store.orgstore.read", "/erp-mdm/hxl.erp.store.update.import", "/erp-mdm/hxl.erp.store.batchupdate", "/erp-mdm/hxl.erp.store.usercenterid.synchronize", "/erp-mdm/hxl.erp.store.citycode.update", "/erp-mdm/hxl.erp.storerent.import", "/erp-mdm/hxl.erp.store.customattribute.update", "/erp-mdm/hxl.erp.store.cargoownerdelivery.short.page", "/erp-mdm/hxl.erp.store.synchronize", "/erp-mdm/hxl.erp.store.short.settlement.page", "/erp-mdm/hxl.erp.storerent.importV2", "/erp-mdm/hxl.erp.store.dms.find", "/erp-mdm/hxl.erp.store.shortfind", "/erp-mdm/hxl.erp.store.food.waithandle.find", "/erp-mdm/hxl.erp.store.area.detail.find", "/erp-mdm/hxl.erp.store.short.read", "/erp-mdm/hxl.erp.storenametemplate.download", "/erp-mdm/hxl.erp.store.page", "/erp-mdm/hxl.erp.store.auth.store.find", "/erp-mdm/hxl.erp.storefee.page", "/erp-mdm/hxl.erp.storefee.read", "/erp-mdm/hxl.erp.storefileapply.read", "/erp-mdm/hxl.erp.storefileapply.save", "/erp-mdm/hxl.erp.storefileapply.pass", "/erp-mdm/hxl.erp.storefileapply.update", "/erp-mdm/hxl.erp.storefileapply.refuse", "/erp-mdm/hxl.erp.storefileapply.waithandle.find", "/erp-mdm/hxl.erp.storegroup.import", "/erp-mdm/hxl.erp.storegroup.update", "/erp-mdm/hxl.erp.storegroup.find", "/erp-mdm/hxl.erp.storegroup.save", "/erp-mdm/hxl.erp.storegroup.delete", "/erp-mdm/hxl.erp.storehardware.brand.save", "/erp-mdm/hxl.erp.storehardware.brand.findByCategory", "/erp-mdm/hxl.erp.storehardwarebrand.read", "/erp-mdm/hxl.erp.storehardwarebrand.export", "/erp-mdm/hxl.erp.storehardware.brand.update", "/erp-mdm/hxl.erp.storehardware.brand.delete", "/erp-mdm/hxl.erp.storehardware.brand.find", "/erp-mdm/hxl.erp.storehardware.category.update", "/erp-mdm/hxl.erp.storehardware.category.readByName", "/erp-mdm/hxl.erp.storehardware.category.read", "/erp-mdm/hxl.erp.storehardware.category.delete", "/erp-mdm/hxl.erp.storehardware.category.find", "/erp-mdm/hxl.erp.storehardware.category.save", "/erp-mdm/hxl.erp.storehardware.save", "/erp-mdm/hxl.erp.storehardware.update", "/erp-mdm/hxl.erp.storehardware.template.download", "/erp-mdm/hxl.erp.storehardware.read", "/erp-mdm/hxl.erp.storehardware.delete", "/erp-mdm/hxl.erp.storehardware.import", "/erp-mdm/hxl.erp.storehardware.find", "/erp-mdm/hxl.erp.storehardware.upload", "/erp-mdm/hxl.erp.storehardware.export", "/erp-mdm/hxl.erp.storehouse.pos.find", "/erp-mdm/hxl.erp.storehousenametemplate.download", "/erp-mdm/hxl.erp.storehouse.update", "/erp-mdm/hxl.erp.storehouse.contact.find", "/erp-mdm/hxl.erp.storehouse.read", "/erp-mdm/hxl.erp.storehouse.centerstoreid.find", "/erp-mdm/hxl.erp.storehouse.code.get", "/erp-mdm/hxl.erp.storehousename.import", "/erp-mdm/hxl.erp.storehouse.scm.read", "/erp-mdm/hxl.erp.storehouse.save", "/erp-mdm/hxl.erp.storehouse.page", "/erp-mdm/hxl.erp.storehouse.delete", "/erp-mdm/hxl.erp.storehouse.batchsave", "/erp-mdm/hxl.erp.storehouse.store.find", "/erp-mdm/hxl.erp.storelabel.find", "/erp-mdm/hxl.erp.storelabel.update", "/erp-mdm/hxl.erp.storelabel.delete", "/erp-mdm/hxl.erp.storelabel.store.find", "/erp-mdm/hxl.erp.storelabel.batchdelete", "/erp-mdm/hxl.erp.storelabel.batchsave", "/erp-mdm/hxl.erp.storelabel.read", "/erp-mdm/hxl.erp.storelabel.export", "/erp-mdm/hxl.erp.storelabel.save", "/erp-mdm/hxl.erp.storelabel.batchexport", "/erp-mdm/hxl.erp.storelabel.import", "/erp-mdm/hxl.erp.supplierhistory.init", "/erp-mdm/hxl.erp.supplierhistory.page", "/erp-mdm/hxl.erp.supplierhistory.read", "/erp-mdm/hxl.erp.suppliercategory.save", "/erp-mdm/hxl.erp.suppliercategory.findwithsupplier", "/erp-mdm/hxl.erp.suppliercategory.update", "/erp-mdm/hxl.erp.suppliercategory.center.find", "/erp-mdm/hxl.erp.suppliercategory.find", "/erp-mdm/hxl.erp.suppliercategory.scm.check", "/erp-mdm/hxl.erp.suppliercategory.delete", "/erp-mdm/hxl.erp.suppliercontract.invalid", "/erp-mdm/hxl.erp.suppliercontract.update", "/erp-mdm/hxl.erp.suppliercontract.audit", "/erp-mdm/hxl.erp.suppliercontract.read", "/erp-mdm/hxl.erp.suppliercontract.item.export", "/erp-mdm/hxl.erp.suppliercontract.unloadingfee.update", "/erp-mdm/hxl.erp.suppliercontract.file.upload", "/erp-mdm/hxl.erp.suppliercontract.reaudit", "/erp-mdm/hxl.erp.suppliercontract.batchdelete", "/erp-mdm/hxl.erp.suppliercontract.page", "/erp-mdm/hxl.erp.suppliercontract.save", "/erp-mdm/hxl.erp.suppliernametemplate.download", "/erp-mdm/hxl.erp.supplier.children.find", "/erp-mdm/hxl.erp.supplier.account.handle", "/erp-mdm/hxl.erp.supplier.center.association.find", "/erp-mdm/hxl.erp.supplier.unloadtype.read", "/erp-mdm/hxl.erp.supplier.batch.update", "/erp-mdm/hxl.erp.supplier.update", "/erp-mdm/hxl.erp.supplier.producerandexecutivestandard.find", "/erp-mdm/hxl.erp.supplier.pdf.convert.img", "/erp-mdm/hxl.erp.supplier.center.short.page", "/erp-mdm/hxl.erp.suppliertemplate.download", "/erp-mdm/hxl.erp.supplier.import", "/erp-mdm/hxl.erp.scm.supplier.check", "/erp-mdm/hxl.erp.supplier.export", "/erp-mdm/hxl.erp.supplier.batch.update.cnt", "/erp-mdm/hxl.erp.suppliername.import", "/erp-mdm/hxl.erp.supplier.delete", "/erp-mdm/hxl.erp.supplier.center.read", "/erp-mdm/hxl.erp.supplierassociationsupplier.export", "/erp-mdm/hxl.erp.supplier.read", "/erp-mdm/hxl.erp.supplier.merchantstatus.enable", "/erp-mdm/hxl.erp.supplier.producer.find", "/erp-mdm/hxl.erp.supplier.code.get", "/erp-mdm/hxl.erp.supplier.read.supplier", "/erp-mdm/hxl.erp.supplier.sharestore.import", "/erp-mdm/hxl.erp.supplier.account.find", "/erp-mdm/hxl.erp.supplieruser.copy", "/erp-mdm/hxl.erp.supplier.platformmoneymanagement.enable", "/erp-mdm/hxl.erp.supplier.short.page", "/erp-mdm/hxl.erp.supplier.app.update", "/erp-mdm/hxl.erp.supplier.balance.read", "/erp-mdm/hxl.erp.supplier.page", "/erp-mdm/hxl.erp.supplier.batch.update.count", "/erp-mdm/hxl.erp.supplier.save", "/erp-mdm/hxl.erp.supplier.getsharestore", "/erp-mdm/hxl.erp.parentassociationchild.export", "/erp-mdm/hxl.erp.supplier.findbyupdatetime", "/erp-mdm/hxl.erp.supplier.auth.check", "/erp-mdm/hxl.erp.supplier.file.handle", "/erp-mdm/hxl.erp.supplier.sharestoretemplate.download", "/erp-mdm/hxl.erp.supplier.children.batchfind", "/erp-mdm/hxl.erp.supplier.label.save", "/erp-mdm/hxl.erp.supplier.label.export", "/erp-mdm/hxl.erp.suppliermainbody.update", "/erp-mdm/hxl.erp.suppliermainbody.find", "/erp-mdm/hxl.erp.suppliermainbody.delete", "/erp-mdm/hxl.erp.suppliermainbody.save", "/erp-mdm/hxl.erp.supplierqualityorder.comment.save", "/erp-mdm/hxl.erp.supplierqualityorder.submit", "/erp-mdm/hxl.erp.supplierqualityorder.page", "/erp-mdm/hxl.erp.supplierqualityorder.invalid", "/erp-mdm/hxl.erp.supplierqualityorder.param.find", "/erp-mdm/hxl.erp.supplierqualityorder.file.upload", "/erp-mdm/hxl.erp.supplierqualityorder.pass", "/erp-mdm/hxl.erp.supplierqualityorder.save", "/erp-mdm/hxl.erp.supplierqualityorder.update", "/erp-mdm/hxl.erp.supplierqualityorder.item.page", "/erp-mdm/hxl.erp.supplierqualityorder.export", "/erp-mdm/hxl.erp.supplierqualityorder.read", "/erp-mdm/hxl.erp.supplierqualityorder.deny", "/erp-mdm/hxl.erp.userapplyorder.deny", "/erp-mdm/hxl.erp.userapplyorder.audit", "/erp-mdm/hxl.erp.userapplyorder.delete", "/erp-mdm/hxl.erp.userapplyorder.approve", "/erp-mdm/hxl.erp.userapplyorder.alterhistory", "/erp-mdm/hxl.erp.userapplyorder.read", "/erp-mdm/hxl.erp.userapplyorder.update", "/erp-mdm/hxl.erp.userapplyorder.refuse", "/erp-mdm/hxl.erp.userapplyorder.pass", "/erp-mdm/hxl.erp.userapplyorder.saveandaudit", "/erp-mdm/hxl.erp.userapplyorder.save", "/erp-mdm/hxl.erp.userapplyorder.modify", "/erp-mdm/hxl.erp.userapplyorder.softdelete", "/erp-mdm/hxl.erp.userapplyorder.page", "/erp-mdm/hxl.erp.userapplyrole.role.find", "/erp-mdm/hxl.erp.userapplyrole.delete", "/erp-mdm/hxl.erp.userapplyrole.userattribute.find", "/erp-mdm/hxl.erp.userapplyrole.user.find", "/erp-mdm/hxl.erp.userapplyrole.read", "/erp-mdm/hxl.erp.userapplyrole.attribute.find", "/erp-mdm/hxl.erp.userapplyrole.update", "/erp-mdm/hxl.erp.userapplyrole.page", "/erp-mdm/hxl.erp.userapplyrole.save", "/erp-mdm/hxl.erp.usercolumn.get", "/erp-mdm/hxl.erp.usercolumn.update", "/erp-mdm/hxl.erp.user.tel.find", "/erp-mdm/hxl.erp.userphone.import", "/erp-mdm/hxl.erp.user.supplier.batchupdate.count", "/erp-mdm/hxl.erp.user.logincode.send", "/erp-mdm/hxl.erp.thirdparty.auth", "/erp-mdm/hxl.erp.user.account.app.org.find", "/erp-mdm/hxl.erp.user.pos.batchupdate.count", "/erp-mdm/hxl.erp.user.pos.export", "/erp-mdm/hxl.erp.user.miniapp.find", "/erp-mdm/hxl.erp.usertemplate.pos.download", "/erp-mdm/hxl.erp.user.temp.save", "/erp-mdm/hxl.erp.user.notice.status.read", "/erp-mdm/hxl.erp.user.client.export", "/erp-mdm/hxl.erp.user.switchstore.page", "/erp-mdm/hxl.erp.user.supplier.export", "/erp-mdm/hxl.erp.user.supplier.batchupdate", "/erp-mdm/hxl.erp.user.pos.save", "/erp-mdm/hxl.erp.user.alterlog", "/erp-mdm/hxl.erp.outeruser.update.import", "/erp-mdm/hxl.erp.usertemplate.supplier.download", "/erp-mdm/hxl.erp.user.findbynameandaccount", "/erp-mdm/hxl.erp.user.pos.import", "/erp-mdm/hxl.erp.outeruser.export", "/erp-mdm/hxl.erp.user.server.login", "/erp-mdm/hxl.erp.user.read", "/erp-mdm/hxl.erp.user.batchlogout", "/erp-mdm/hxl.erp.user.id.find", "/erp-mdm/hxl.erp.user.self.update", "/erp-mdm/hxl.erp.user.client.import", "/erp-mdm/hxl.erp.user.batchupdate.count", "/erp-mdm/hxl.erp.usertemplate.client.download", "/erp-mdm/hxl.erp.usernametemplate.download", "/erp-mdm/hxl.erp.usersupdatetemplate.client.download", "/erp-mdm/hxl.erp.user.account.app.login", "/erp-mdm/hxl.erp.user.findByAuthorityIds", "/erp-mdm/hxl.erp.user.pos.page", "/erp-mdm/hxl.erp.user.client.update", "/erp-mdm/hxl.erp.user.supplier.update", "/erp-mdm/hxl.erp.user.pos.update.import", "/erp-mdm/hxl.erp.user.changepwdtelcode.send", "/erp-mdm/hxl.erp.user.client.update.import", "/erp-mdm/hxl.erp.user.findbyupdatetime", "/erp-mdm/hxl.erp.user.psw.update", "/erp-mdm/hxl.erp.user.account.login", "/erp-mdm/hxl.erp.user.client.page", "/erp-mdm/hxl.erp.user.wechat.bind", "/erp-mdm/hxl.erp.user.pos.login", "/erp-mdm/hxl.erp.user.changetelcodechenk.send", "/erp-mdm/hxl.erp.curruser.wechat.bind", "/erp-mdm/hxl.erp.user.supplier.page", "/erp-mdm/hxl.erp.user.querysupplierids.find", "/erp-mdm/hxl.erp.user.supplier.import", "/erp-mdm/hxl.erp.user.ama", "/erp-mdm/hxl.erp.user.update", "/erp-mdm/hxl.erp.user.store.switch", "/erp-mdm/hxl.erp.user.wechat.unbind", "/erp-mdm/hxl.erp.user.tel.change", "/erp-mdm/hxl.erp.user.addamauser", "/erp-mdm/hxl.erp.storeuser.page", "/erp-mdm/hxl.erp.user.checkcode.send", "/erp-mdm/hxl.erp.user.batchdelete", "/erp-mdm/hxl.erp.user.import", "/erp-mdm/hxl.erp.user.changetelcode.send", "/erp-mdm/hxl.erp.user.pos.update", "/erp-mdm/hxl.erp.user.resetcode.send", "/erp-mdm/hxl.erp.user.temp.login", "/erp-mdm/hxl.erp.user.page", "/erp-mdm/hxl.erp.user.findbyaccount", "/erp-mdm/hxl.erp.user.app.qrcode.scan", "/erp-mdm/hxl.erp.user.resetpwdvoicecode.send", "/erp-mdm/hxl.erp.user.pos.batchupdate", "/erp-mdm/hxl.erp.user.save", "/erp-mdm/hxl.erp.user.weixintoken.refresh", "/erp-mdm/hxl.erp.user.export", "/erp-mdm/hxl.erp.user.client.find", "/erp-mdm/hxl.erp.user.test.wechat.qrcode.get", "/erp-mdm/hxl.erp.user.querysupplieridandstoreids.find", "/erp-mdm/hxl.erp.userhistory.init", "/erp-mdm/hxl.erp.usersupdatetemplate.pos.download", "/erp-mdm/hxl.erp.user.wechat.login", "/erp-mdm/hxl.erp.user.supplier.save", "/erp-mdm/hxl.erp.xlbusertemplate.download", "/erp-mdm/hxl.erp.user.outer.page", "/erp-mdm/hxl.erp.user.findbytel", "/erp-mdm/hxl.erp.user.find", "/erp-mdm/hxl.erp.user.tel.read", "/erp-mdm/hxl.erp.user.supplier.update.import", "/erp-mdm/weixin.mp.oauth2.callback", "/erp-mdm/hxl.erp.outeruser.updatetemplate.download", "/erp-mdm/hxl.erp.user.logout", "/erp-mdm/hxl.erp.user.client.save", "/erp-mdm/hxl.erp.user.app.login", "/erp-mdm/hxl.erp.username.import", "/erp-mdm/hxl.erp.user.changetelcodechenk.confirm", "/erp-mdm/hxl.erp.user.pwd.reset", "/erp-mdm/hxl.erp.user.token.refresh", "/erp-mdm/hxl.erp.user.app.qrcode.verify", "/erp-mdm/hxl.erp.xlbuser.import", "/erp-mdm/hxl.erp.user.miniapp.logincode.send", "/erp-mdm/hxl.erp.user.login", "/erp-mdm/hxl.erp.user.app.qrcode.create", "/erp-mdm/hxl.erp.user.image.update", "/erp-mdm/hxl.erp.user.notice.status.update", "/erp-mdm/hxl.erp.user.item.find", "/erp-mdm/hxl.erp.userposition.find", "/erp-mdm/hxl.erp.outeruser.template.download", "/erp-mdm/hxl.erp.user.all.page", "/erp-mdm/hxl.erp.user.voicecode.send", "/erp-mdm/hxl.erp.outeruser.import", "/erp-mdm/hxl.erp.userphonetemplate.download", "/erp-mdm/hxl.erp.user.batchupdate", "/erp-mdm/hxl.erp.usertemplate.download", "/erp-mdm/hxl.erp.user.app.update", "/erp-mdm/hxl.erp.user.server.save", "/erp-mdm/hxl.erp.user.update.import", "/erp-mdm/hxl.erp.user.delete", "/erp-mdm/hxl.erp.usersupdatetemplate.supplier.download", "/erp-mdm/hxl.erp.userhistory.add", "/erp-mdm/hxl.erp.user.resetpwdcode.send", "/erp-mdm/hxl.erp.user.wechat.qrcode.get", "/erp-mdm/hxl.erp.usersupdatetemplate.download", "/erp-mdm/hxl.erp.user.resetpwd.confirm", "/erp-mdm/hxl.erp.userdept.update", "/erp-mdm/hxl.erp.userdept.delete", "/erp-mdm/hxl.erp.userdept.find", "/erp-mdm/hxl.erp.userdept.save", "/erp-mdm/hxl.erp.userlocation.batchsave", "/erp-mdm/hxl.erp.userlocation.save", "/erp-mdm/hxl.erp.userlocation.getlatest", "/erp-mdm/hxl.erp.userlog.printscreen.save", "/erp-mdm/hxl.erp.userlog.export", "/erp-mdm/hxl.erp.userlog.page", "/erp-mdm/hxl.erp.user.store.storagecategory.find", "/erp-mdm/hxl.erp.user.store.storagecategory.export", "/erp-mdm/starter.progress.interrupt", "/erp-mdm/starter.progress.create", "/erp-mdm/starter.progress.query", "/erp-mdm/hxl.erp.account.user.find", "/erp-mdm/hxl.erp.account.codelogin.user.find", "/erp-mdm/hxl.erp.user.resetpwd.find", "/erp-mdm/hxl.erp.account.captchaverify", "/erp-mdm/hxl.erp.account.pwd.user.find", "/erp-mdm/hxl.erp.account.wechat.login", "/erp-mdm/hxl.erp.app.weather.find", "/erp-mdm/hxl.erp.metadata.saveorupdate", "/erp-mdm/hxl.erp.metadata.queryModelViewMetaData", "/erp-mdm/idaas.erp.user.profile", "/erp-mdm/hxl.erp.store.short.code.find", "/erp-mdm/hxl.erp.user.bind.customerservice", "/erp-mdm/hxl.erp.service.account.page", "/erp-mdm/hxl.erp.service.account.grant", "/erp-mdm/hxl.erp.service.account.terminate", "/erp-mdm/hxl.erp.user.tel.confirm", "/erp-mdm/hxl.erp.user.app.qrcode.query", "/erp-mdm/hxl.erp.role.authority.find", "/erp-mdm/hxl.erp.external.platform.store.log.export", "/erp-mdm/erp.metadata.queryCustomModelViewMetaData", "/erp-mdm/erp.metadata.queryModelViewMetaData", "/erp-mdm/hxl.erp.storehistory.read", "/erp-mdm/hxl.erp.store.external.platform.store.create.retry", "/erp-mdm/hxl.erp.storerelocation.read", "/erp-mdm/hxl.erp.storemodification.find", "/erp-mdm/hxl.erp.supplier.organization.file.upload", "/erp-mdm/hxl.erp.supplier.organization.apply.query", "/erp-mdm/hxl.erp.supplier.organization.approve.create", "/erp-mdm/hxl.erp.supplier.organization.order.query", "/erp-mdm/hxl.erp.org.skulimit.read", "/erp-mdm/hxl.erp.org.tree.invoice"]