# API接口路径分析报告

## 报告概述

本报告分析了项目中ERP接口路径的替换情况和当前使用状态。

**生成时间**: 2025/8/4 14:55:29

---

## 任务1：接口路径替换结果

### 替换概述
- **处理文件数**: 84
- **修改文件数**: 84
- **总替换数**: 214
- **替换状态**: ✅ 成功完成，所有匹配的 `/erp/` 路径都已替换为 `/erp-mdm/`

### 主要替换文件
- **src\api\common.ts**: 1 个替换
- **src\constants\baseDataConfig.tsx**: 45 个替换
- **src\data\common\fieldListConfig.tsx**: 3 个替换
- **src\data\common\fieldModule\devicesBrand.tsx**: 1 个替换
- **src\data\common\fieldModule\interWarehouseTransfer.tsx**: 1 个替换
- **src\data\common\fieldModule\purchaseOrdering.tsx**: 1 个替换
- **src\data\common\fieldModule\stockCheckOrder.tsx**: 1 个替换
- **src\data\common\fieldModule\storeDeviceManage.tsx**: 1 个替换
- **src\data\common\fieldModule\storeManage.tsx**: 1 个替换
- **src\data\common\fieldModule\storeOrder.tsx**: 2 个替换


... 以及其他 74 个文件

---

## 任务2：/erp-mdm/ 接口使用分析

### 分析概述
- **检查文件数**: 1343
- **发现的接口总数**: 1343
- **去重后的接口数量**: 187
- **在api-values.json中存在**: 182
- **在api-values.json中缺失**: 5
- **已知API总数**: 912

### 覆盖率分析
- **接口覆盖率**: 97.33%
- **匹配度**: 19.96%

---

## 缺失接口详情

❌ **发现 5 个缺失接口**


### 1. `/erp-mdm/`

**使用位置**:
- **文件**: src\utils\purchaseUrl.ts
  - **行号**: 205
  - **模块**: utils/purchaseUrl.ts
  - **上下文**: `return url?.replace('/erp-mdm/', '/purchase/') + (param ? `?${param}` : '')`
  - **变量分析**: 包含变量但非ERP相关


### 2. `/erp-mdm/hxl.erp.itemunit.`

**使用位置**:
- **文件**: src\pages\archives\goodsUnits\server.ts
  - **行号**: 4
  - **模块**: archives/goodsUnits
  - **上下文**: `const urlHeader = '/erp-mdm/hxl.erp.itemunit.';`
  
- **文件**: src\pages\archives\goodsUnits\server.ts
  - **行号**: 4
  - **模块**: archives/goodsUnits
  - **上下文**: `const urlHeader = '/erp-mdm/hxl.erp.itemunit.';`
  


### 3. `/erp-mdm/hxl.erp.org.business.scope.item.export `

**使用位置**:
- **文件**: src\pages\archives\orgBusinessArea\index.tsx
  - **行号**: 129
  - **模块**: archives/orgBusinessArea
  - **上下文**: `? '/erp-mdm/hxl.erp.org.business.scope.item.export '`
  


### 4. `/erp-mdm/hxl.erp.org.skulimit.read`

**使用位置**:
- **文件**: src\pages\archives\skuCeilingManagement\item\index.tsx
  - **行号**: 103
  - **模块**: archives/skuCeilingManagement
  - **上下文**: `url={'/erp-mdm/hxl.erp.org.skulimit.read'}`
  
- **文件**: src\pages\archives\skuCeilingManagement\item\index.tsx
  - **行号**: 103
  - **模块**: archives/skuCeilingManagement
  - **上下文**: `url={'/erp-mdm/hxl.erp.org.skulimit.read'}`
  


### 5. `/erp-mdm/hxl.erp.org.tree.invoice`

**使用位置**:
- **文件**: src\data\common\fieldListConfig.tsx
  - **行号**: 733
  - **模块**: common/fieldListConfig.tsx
  - **上下文**: `url: '/erp-mdm/hxl.erp.org.tree.invoice',`
  
- **文件**: src\data\common\fieldListConfig.tsx
  - **行号**: 733
  - **模块**: common/fieldListConfig.tsx
  - **上下文**: `url: '/erp-mdm/hxl.erp.org.tree.invoice',`
  


---

## 接口使用统计

### 按模块分组的接口使用情况
- **constants/baseDataConfig.tsx**: 270 次使用, 37 个不同接口 (270 存在, 0 缺失)
- **common/fieldListConfig.tsx**: 82 次使用, 11 个不同接口 (80 存在, 2 缺失)
- **archives/storeArea**: 50 次使用, 16 个不同接口 (50 存在, 0 缺失)
- **archives/cargoOwnerCenter**: 36 次使用, 12 个不同接口 (36 存在, 0 缺失)
- **archives/contractMangement**: 35 次使用, 10 个不同接口 (35 存在, 0 缺失)
- **archives/skuCeilingManagement**: 34 次使用, 15 个不同接口 (32 存在, 2 缺失)
- **archives/goodsOrder**: 32 次使用, 6 个不同接口 (32 存在, 0 缺失)
- **archives/devicesBrand**: 26 次使用, 8 个不同接口 (26 存在, 0 缺失)
- **procurement/supplierRelationshipManagement**: 24 次使用, 7 个不同接口 (24 存在, 0 缺失)
- **archives/cargoOwner**: 24 次使用, 10 个不同接口 (24 存在, 0 缺失)
- **delivery/stockForecasts**: 24 次使用, 8 个不同接口 (24 存在, 0 缺失)
- **delivery/deliveryCenterStore**: 24 次使用, 9 个不同接口 (24 存在, 0 缺失)
- **wholesale/wholesalePrice**: 22 次使用, 7 个不同接口 (22 存在, 0 缺失)
- **delivery/storeOrders**: 22 次使用, 8 个不同接口 (22 存在, 0 缺失)
- **fieldModule/userManage.tsx**: 21 次使用, 3 个不同接口 (21 存在, 0 缺失)

---

## 建议和后续行动

### 1. 缺失接口处理

- 需要将以下接口添加到api-values.json中：
  - `/erp-mdm/`
  - `/erp-mdm/hxl.erp.itemunit.`
  - `/erp-mdm/hxl.erp.org.business.scope.item.export `
  - `/erp-mdm/hxl.erp.org.skulimit.read`
  - `/erp-mdm/hxl.erp.org.tree.invoice`


### 2. 代码质量建议
- 建议统一接口路径的定义方式，避免硬编码
- 考虑将接口路径集中管理到配置文件中
- 对于包含变量的路径，建议添加注释说明变量的可能值

### 3. 验证建议
- 定期运行此分析脚本，确保新增接口都在api-values.json中
- 建立CI/CD检查，防止遗漏新接口

---

## 附录

### 完整的缺失接口列表
```json
[
  "/erp-mdm/",
  "/erp-mdm/hxl.erp.itemunit.",
  "/erp-mdm/hxl.erp.org.business.scope.item.export ",
  "/erp-mdm/hxl.erp.org.skulimit.read",
  "/erp-mdm/hxl.erp.org.tree.invoice"
]
```

### 分析数据摘要
```json
{
  "totalFoundApis": 1343,
  "uniqueApis": 187,
  "existingInApiValues": 182,
  "missingInApiValues": 5,
  "knownApiCount": 912
}
```

---

*报告生成完成 - 2025/8/4 14:55:29*
