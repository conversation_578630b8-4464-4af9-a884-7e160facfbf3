import RelateStoresCreate from '@/components/relateStoreCreate';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbInput,
  type BasicDataConfig,
  type SelectTreeType,
  type SelectType,
} from '@xlb/components';
import type { FormInstance } from 'antd';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { accountType, cargoList, itemProperty, userDept } from './data';
export const config: BasicDataConfig = {
  /**组织 */
  organization: {
    title: '选择组织',
    url: '/erp-mdm/hxl.erp.org.find',
    columns: [
      {
        name: '序号',
        code: '_index',
        align: 'center',
        width: 50,
      },
      {
        name: '组织名称',
        code: 'name',
        // 组织find接口暂未支持排序
        // features: { sortable: true },
        width: 200,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
      },
    ],
    tableProps: {
      hideOnSinglePage: true,
    },
  },
  basket: {
    title: '选择物资载具',
    url: '/erp/hxl.erp.basket.page',
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
    columns: [
      {
        name: '物资载具名称',
        code: 'name',
        width: 200,
      },
      {
        name: '单价',
        code: 'price',
        width: 120,
        render: (value) => {
          return `${value?.toFixed(4)}`;
        },
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
  },
  storeArea: {
    title: '选择门店区域',
    url: '/erp-mdm/hxl.erp.storearea.find',
    columns: [
      {
        name: '序号',
        code: '_index',
        width: 70,
      },
      {
        name: '门店区域名称',
        code: 'name',
        width: 160,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
  },
  userdept: {
    title: '选择用户部门',
    url: '/erp-mdm/hxl.erp.userdept.find',
    columns: [
      {
        name: '用户部门名称',
        code: 'name',
        width: 150,
      },
      {
        name: '部门负责人',
        code: 'leader',
        width: 150,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
  },
  // 选择门店标签
  storeLabels: {
    title: '选择门店标签',
    url: '/erp/hxl.erp.storelabel.find',
    columns: [
      {
        name: '门店标签名称',
        code: 'store_label_name',
        width: 280,
      },
      {
        name: '应用用户部门',
        code: 'user_depts',
        width: 200,
        render(text) {
          return <span>{text?.map((v: any) => v.name).join(',')}</span>;
        },
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
      {
        type: 'inputDialog',
        label: '应用用户部门',
        name: 'user_dept_ids',
        dialogParams: {
          type: 'userdept',
          dataType: 'lists',
          isLeftColumn: false,
          isMultiple: true,
        },
      },
    ],
  },
  //
  // 选择商品标签
  itemLabels: {
    title: '选择商品标签',
    url: '/erp-mdm/hxl.erp.itemlabel.find',
    columns: [
      {
        name: '商品标签名称',
        code: 'item_label_name',
        width: 280,
      },
      {
        name: '应用用户部门',
        code: 'user_depts',
        width: 200,
        render(text) {
          return <span>{text?.map((v: any) => v.name).join(',')}</span>;
        },
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
      {
        type: 'inputDialog',
        label: '应用用户部门',
        name: 'user_dept_ids',
        dialogParams: {
          type: 'userdept',
          dataType: 'lists',
          isLeftColumn: false,
          isMultiple: true,
        },
      },
    ],
  },

  // 选择商品分类
  goodsCategory: {
    title: '选择商品分类',
    url: '/erp-mdm/hxl.erp.category.find',
    columns: [
      {
        name: '商品标签名称',
        code: 'name',
        width: 280,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
    fieldName: {
      id: 'id',
      parent_id: 'parent_id',
    },
  },
  storeAreaCategories: {
    title: '选择门店区域分类',
    url: '/erp-mdm/hxl.erp.storeareacategory.find',
    columns: [
      {
        name: '区域分类',
        code: 'name',
        width: 160,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
  },
  // #region业务区域
  businessArea: {
    title: '业务区域', // 标题
    url: '/erp-mdm/hxl.erp.businessarea.detail.find', // 请求地址
    // leftUrl: '/erp-mdm/hxl.erp.businessarea.find',
    withSelect: true,
    selectOptions: (settings, data) => {
      if (Object.keys(settings).length > 0) {
        const _data = Object.values(settings)?.map((v: any, index: number) => {
          return {
            value: index + 2,
            label: v.name,
            reqUrl: `/erp-mdm/hxl.erp.businessarea.find`,
            id: v.id,
            data: {
              org_ids: [v.id],
            },
          };
        });
        const _data2 = [
          {
            value: 1,
            label: '全部组织',
            reqUrl: '/erp-mdm/hxl.erp.businessarea.find',
            id: 0,
            requestParams: {
              org_ids: [0],
            },
            data: {
              org_ids: [0],
            },
          },
          ..._data,
        ];
        return _data2;
      }
      return [
        {
          value: 1,
          label: '全部组织',
          reqUrl: '/erp-mdm/hxl.erp.businessarea.find',
          id: 0,
          data: {
            org_ids: [0],
          },
        },
      ];
    },
    leftKey: 'id',
    resetForm: true,
    primaryKey: 'id',
    columns: (settings, data, changeValues) => {
      const level = changeValues?.leftSelectNodeData?.level || 6;
      return [
        {
          name: '一级业务区域',
          code: 'first_name',
          width: 130,
        },
        {
          name: '二级业务区域',
          code: 'second_name',
          width: 130,
          hidden: level < 2,
        },
        {
          name: '三级业务区域',
          code: 'third_name',
          width: 130,
          hidden: level < 3,
        },
        {
          name: '四级业务区域',
          code: 'four_name',
          width: 130,
          hidden: level < 4,
        },
        {
          name: '五级业务区域',
          code: 'five_name',
          width: 130,
          hidden: level < 5,
        },
        {
          name: '六级业务区域',
          code: 'six_name',
          width: 130,
          hidden: level < 6,
        },
        {
          name: '关联门店数',
          code: 'count',
          widt: 120,
          render: (value, record) => {
            return (
              <div
                className="link"
                onClick={(e) => {
                  e.stopPropagation();
                  NiceModal.show(RelateStoresCreate, {
                    relateStoreRecord: record,
                  });
                }}
              >
                {value}
              </div>
            );
          },
        },
      ];
    },
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
    prevPost(params: any) {
      return {
        ...params,
        id: params?.id,
      };
    },
    afterPost(data: any) {
      const dataValueDetails = data?.value_details?.map((v) => {
        return {
          ...v,
          show_name:
            v.six_name ||
            v.five_name ||
            v.four_name ||
            v.third_name ||
            v.second_name ||
            v.first_name,
        };
      });
      return dataValueDetails;
    },
    settingProps: {
      url: '/erp-mdm/hxl.erp.org.find',
      beforePost: () => {
        return {
          level: 2,
        };
      },
    },
    fieldName: {
      idKey: 'id',
      nameKey: 'show_name',
    },
  },
  // 角色
  role: {
    title: '选择用户角色',
    url: '/erp-mdm/hxl.erp.role.page',
    columns: [
      {
        title: '角色名称',
        code: 'name',
        width: 230,
      },
      {
        title: '角色分类',
        code: 'category_name',
        width: 180,
      },
    ],
    formList: [{ label: '关键字', name: 'keyword', type: 'input' }],
  },
  // 选择供应商角色
  supplierRole: {
    title: '选择供应商角色',
    url: '/erp-mdm/hxl.erp.role.page',
    columns: [
      {
        title: '角色名称',
        code: 'name',
        width: 100,
      },
    ],
    formList: [{ label: '关键字', name: 'keyword', type: 'input' }],
  },
  // 选择批发角色
  clientRole: {
    title: '选择批发角色',
    url: '/erp-mdm/hxl.erp.role.page',
    columns: [
      {
        title: '角色名称',
        code: 'name',
        width: 100,
      },
    ],
    formList: [{ label: '关键字', name: 'keyword', type: 'input' }],
  },
  // 选择客户
  crm_client_select: {
    title: '选择客户',
    url: '/kms/hxl.kms.client.contracttemplate.find',
    resetForm: true,
    formList: [
      {
        type: 'input',
        label: '客户姓名',
        name: 'name',
      },
      {
        type: 'input',
        label: '手机号',
        name: 'phone',
      },
    ],
    columns: [
      {
        name: '客户姓名',
        code: 'name',
        width: 150,
      },
      {
        name: '手机号',
        code: 'phone',
        width: 150,
      },
    ],
  },
  // 选择门店
  store: {
    title: '选择门店',
    url: '/erp-mdm/hxl.erp.store.short.page',
    leftUrl: '/erp-mdm/hxl.erp.storegroup.find',
    selectedAllUrl: '/erp-mdm/hxl.erp.store.ids.find', //全选接口
    resetForm: true,
    columns: (settings) => [
      {
        name: '代码',
        code: 'store_code',
        width: 110,
      },
      {
        name: '名称',
        code: 'store_name',
        width: 265,
      },
      {
        name: '营业执照名称',
        code: 'license_name',
        width: 240,
      },
      {
        name: '执照类型',
        code: 'license_type',
        width: 100,
        render(text) {
          const obj: any = {
            COMPANY: '公司',
            PERSONAL: '个人',
          };
          return obj[text];
        },
      },
      {
        name: '门店分组',
        code: 'store_group',
        width: 100,
        render(text) {
          return text?.name || '';
        },
      },
      {
        name: '配送类型',
        code: 'delivery_type',
        width: 80,
        render(text) {
          const obj: any = {
            JOIN: '加盟',
            DIRECT: '直营',
          };
          return obj[text];
        },
      },
      {
        name: '组织',
        code: 'org_name',
        width: 140,
        hidden: !settings?.enable_organization,
      },
      {
        name: '经营类型',
        code: 'management_type',
        width: 80,
        render(text) {
          const obj: any = {
            0: '直营',
            1: '加盟',
          };
          return obj[text];
        },
      },
    ],
    settingProps: {
      url: '/erp/hxl.erp.baseparam.read',
    },
    formList: (settings, data) => {
      return [
        {
          type: 'input',
          label: '关键字',
          name: 'keyword',
          suffix: true,
        },
        {
          type: 'select',
          label: '门店标签',
          name: 'store_label_ids',
          multiple: true,
          options: [],
          selectRequestParams: {
            url: '/erp/hxl.erp.storelabel.find',
            responseTrans(data) {
              const options: SelectType[] = data.map((item: any) => {
                const obj: SelectType = {
                  label: item.store_label_name,
                  value: item.id,
                };
                return obj;
              });
              return options;
            },
          },
        },
        {
          type: 'select',
          label: '配送类型',
          name: 'delivery_type',
          defaultValue: data?.is_filter_join ? 'JOIN' : '',
          disabled: data?.is_filter_join,
          options: [
            { label: '直营', value: 'DIRECT' },
            { label: '加盟', value: 'JOIN' },
          ],
        },
        {
          type: 'select',
          label: '经营类型',
          name: 'management_type',
          defaultValue: data?.is_filter_join ? '1' : '',
          disabled: data?.is_filter_join,
          options: [
            { label: '直营店', value: '0' },
            { label: '加盟店', value: '1' },
          ],
        },
        {
          type: 'inputDialog',
          label: '行政区域',
          name: 'city_codes',
          treeModalConfig: {
            zIndex: 2002,
            title: '选择区域', // 标题
            url: '/erp-mdm/hxl.erp.store.area.find.all', // 请求地址
            dataType: 'lists',
            checkable: true, // 是否多选
            primaryKey: 'code',
            // data: {
            //   enabled: true
            // },
            // params: {
            //   company_id: LStorage.get('userInfo')?.company_id,
            //   // levels: [0, 1]
            // },
            // requestParams: {
            //   levels: [0, 1]
            // },
            fieldName: {
              id: 'code',
              parent_id: 'parent_code',
            },
            width: 360, // 模态框宽度
          },
          fieldNames: {
            // @ts-ignore
            idKey: 'code',
            nameKey: 'name',
          },
        },
        {
          type: 'inputDialog',
          label: '业务区域',
          name: 'business_area_ids',
          // normalize: (value, preValue, allValues) => {
          //   return value ? value : undefined
          // },
          dialogParams: {
            type: 'businessArea',
            dataType: 'lists',
            isLeftColumn: true,
            isMultiple: true,
            initAfterPost: (data: any) => {
              const dataValueDetails = data?.value_details?.map((v) => {
                return {
                  ...v,
                  show_name:
                    v.six_name ||
                    v.five_name ||
                    v.four_name ||
                    v.third_name ||
                    v.second_name ||
                    v.first_name,
                };
              });
              return {
                list: dataValueDetails,
                total: data?.value_details?.length,
              };
            },

            handleDefaultValue: (data) => {
              const _data = data.map((v) => {
                return {
                  ...v,
                  id: v.id,
                  show_name:
                    v.six_name ||
                    v.five_name ||
                    v.four_name ||
                    v.third_name ||
                    v.second_name ||
                    v.first_name,
                };
              });
              return _data;
            },
          },
          getValueFromEvent: (e: any, arr) => {
            let arrT: any[] = [];
            arr?.forEach((h: any) => {
              arrT = [...arrT, ...h?.ids];
            });
            return arrT;
          },
          fieldNames: {
            idKey: 'id',
            nameKey: 'show_name',
          },
        },
        {
          type: 'compactDatePicker',
          label: '开业日期',
          name: 'opening_time',
          format: 'YYYY-MM-DD',
        },
        {
          type: 'select',
          label: '门店状态',
          name: 'states',
          width: 204,
          options: [
            { value: 'TO_BE_DELIVERED', label: '待交付' },
            { value: 'BUILDING', label: '营建中' },
            { value: 'TO_BE_OPENED', label: '待营业' },
            { value: 'ON_LEAVE', label: '休息中' },
            { value: 'OPENED', label: '营业中' },
            { value: 'CLOSED', label: '已闭店' },
            { value: 'CANCELLED', label: '已取消' },
            { value: 'TO_BE_CLOSED', label: '待停业' },
          ],
          normalize: (value) =>
            value
              ? typeof value === 'string' || typeof value === 'number'
                ? [value]
                : value
              : undefined,
        },
        {
          type: 'checkbox',
          label: '',
          name: 'wait_assign',
          colon: false,
          width: 300,
          hidden: !data?.isShowWaitAssign,
          options: [{ label: '仅查待分配门店', value: 'wait_assign' }],
        },
      ];
    },
    withSelect: true,
    selectOptions: (settings, data) => {
      const _org = data?.org_ids?.length
        ? [data?.org_ids?.[0] ? data?.org_ids?.[0] : 0]
        : [];
      return [
        {
          label: '门店分组',
          value: 'group',
          reqUrl: '/erp-mdm/hxl.erp.storegroup.find',
        },
        {
          label: '业务区域',
          value: 'business',
          reqUrl: '/erp-mdm/hxl.erp.businessarea.find',
          data: {
            org_ids: _org?.filter(Boolean),
            max_level: 3,
          },
        },
        {
          label: '行政区域',
          value: 'admin',

          reqUrl: '/erp-mdm/hxl.erp.store.area.find.all',
          filter: [
            {
              label: '省',
              value: 1,
            },
            {
              label: '市',
              value: 2,
            },
            {
              label: '区',
              value: 3,
            },
          ],
        },
        {
          label: '门店标签',
          value: 'tag',
          reqUrl: '/erp/hxl.erp.storelabel.find',
        },
        settings?.enable_organization && !data?.org_ids?.length
          ? {
              label: '组织分组',
              value: 'org',
              reqUrl: '/erp-mdm/hxl.erp.org.find',
            }
          : null,
      ].filter((item) => !!item) as Array<SelectTreeType>;
    },
    primaryKey: 'id',
    // leftKey: 'store_group_id',
    leftKey: (url: string): string => {
      const urlMatchLeftKey: Record<string, any> = {
        '/erp-mdm/hxl.erp.storegroup.find': {
          isWithChild: true,
          queryParams: 'store_group_ids',
        },
        '/erp-mdm/hxl.erp.businessarea.find': {
          isWithChild: true,
          queryParams: 'business_area_ids',
        },
        '/erp-mdm/hxl.erp.store.area.find.all': {
          isWithChild: true,
          queryParams: 'city_codes',
          fieldName: { parent_id: 'parent_code', id: 'code' },
        },
        '/erp/hxl.erp.storelabel.find': {
          // isWithChild: true,
          queryParams: 'store_label_ids',
          fieldName: { id: 'id', name: 'store_label_name' },
        },
        '/erp-mdm/hxl.erp.org.find': {
          // isWithChild: true,
          queryParams: 'org_ids',
          fieldName: { id: 'id', name: 'name' },
        },
      };
      return urlMatchLeftKey[url];
    },

    // @ts-ignore
    customPrevPost: (params: any, data: any) => {
      const result: any = {};
      Object.keys(data).forEach((key: any) => {
        const val = data[key];
        if (val) {
          result[key] =
            key == 'store_group_ids'
              ? data[key].filter(Boolean)
              : Array.isArray(val)
                ? val
                : [data[key]];
        } else {
          result[key] = undefined;
        }
      });
      return {
        ...params,
        ...result,
        opening_time: params?.opening_time?.length
          ? params.opening_time
          : undefined,
        wait_assign: params?.wait_assign?.includes('wait_assign')
          ? true
          : false,
      };
    },
  },
  // 采购经营范围分类
  purchaseGoods: {
    title: '选择商品',
    url: '/erp-mdm/hxl.erp.item.short.page',
    leftUrl: '/erp-mdm/hxl.erp.category.find',
    resetForm: true,
    dataType: 'lists',
    columns: [
      {
        name: '代码',
        code: 'code',
        width: 80,
      },
      {
        name: '代码',
        code: 'bar_code',
        width: 80,
      },
      {
        name: '商品名称',
        code: 'name',
        width: 150,
        features: {
          sortable: true,
        },
      },
      {
        name: '采购规格',
        code: 'purchase_spec',
        width: 104,
      },
      {
        name: '商品类型',
        code: 'item_type',
        width: 80,
        render(text) {
          const obj: any = {
            // MAINSPEC: '主规格商品',
            // MULTIPLESPEC: '多规格商品',
            STANDARD: '标准商品',
            COMBINATION: '组合商品',
            COMPONENT: '成分商品',
            MAKEBILL: '制单组合',
            GRADING: '分级商品',
          };
          return obj[text];
        },
      },
      {
        name: '基本单位',
        code: 'unit',
        width: 80,
      },
      {
        name: '保质期',
        code: 'period',
        width: 80,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
      {
        type: 'select',
        label: '商品标签',
        name: 'item_label_id',
        selectRequestParams: {
          url: '/erp-mdm/hxl.erp.itemlabel.find',
          responseTrans(data) {
            const options: SelectType[] = data.map((item: any) => {
              const obj: SelectType = {
                label: item.item_label_name,
                value: item.id,
              };
              return obj;
            });
            return options;
          },
        },
        // @ts-ignore
        onChange(e, form: FormInstance) {
          if (!e) {
            form.setFieldValue('item_label_ids', []);
          } else {
            form.setFieldValue('item_label_ids', [e]);
          }
        },
        options: [],
      },
      {
        type: 'input',
        name: 'item_label_ids',
        hidden: true,
      },
    ],
    withSelect: true,
    selectOptions: [
      {
        value: 1,
        label: '商品分类',
        reqUrl: '/erp-mdm/hxl.erp.category.find',
      },
      {
        value: 2,
        label: '商品部门',
        reqUrl: '/erp-mdm/hxl.erp.dept.find',
      },
      {
        value: 3,
        label: '商品品牌',
        reqUrl: '/erp-mdm/hxl.erp.brand.find',
      },
      {
        value: 4,
        label: '供应商',
        reqUrl: '/erp-mdm/hxl.erp.suppliercategory.findwithsupplier',
      },
    ],
    leftKey: (url) => {
      const obj: any = {
        '/erp-mdm/hxl.erp.category.find': {
          queryParams: 'category_ids',
          isWithChild: true,
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp-mdm/hxl.erp.dept.find': {
          queryParams: 'item_dept_id',
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp-mdm/hxl.erp.brand.find': {
          queryParams: 'item_brand_id',
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp-mdm/hxl.erp.suppliercategory.findwithsupplier': {
          queryParams: 'supplier_ids',
          isWithChild: true,
          fieldName: { parent_id: 'parent_id', children: 'suppliers' },
          dataType: 'tree',
        },
      };

      return obj[url];
    },
  },
  // 供应商
  supplier: {
    title: '选择供应商',
    url: '/erp-mdm/hxl.erp.supplier.short.page',
    leftUrl: '/erp-mdm/hxl.erp.suppliercategory.find',
    columns: [
      {
        name: '代码',
        code: 'code',
        width: 100,
      },
      {
        name: '名称',
        code: 'name',
        width: 160,
        features: {
          sortable: true,
        },
      },
      {
        name: '供应商类别',
        code: 'supplier_identity',
        width: 100,
        features: {
          sortable: true,
        },
        render: (text: string) => {
          const email = [
            { label: '父供应商', value: 'PARENT' },
            { label: '子供应商', value: 'CHILD' },
            { label: '普通供应商', value: 'NORMAL' },
          ];
          return (
            <span>{email.find((item) => item.value === text)?.label}</span>
          );
        },
      },
      {
        name: '营业执照名称',
        code: 'letterhead',
        width: 120,
      },
      {
        name: '纳税人类型',
        code: 'taxpayer_type',
        width: 120,
        render(text) {
          const obj: any = {
            0: '一般纳税人',
            1: '小规模纳税人',
          };
          return obj[text];
        },
      },
      {
        name: '类别',
        code: 'supplier_category_name',
        width: 120,
      },
    ],
    //每次打开弹窗自定义请求接口
    settingProps: {
      url: [
        '/erp/hxl.erp.baseparam.read',
        '/erp/hxl.erp.customattribute.show.find',
      ],
      //两个接口请求数据处理
      //注意，url 是数组，beforePost也是数组
      beforePost: () => {
        return [
          {},
          {
            type: 'SUPPLIER',
          },
        ];
      },
      //两个接口返回数据处理 这个地方固定返回数组
      afterPost: (data: any) => {
        const [obj, arr] = data;
        return { ...obj, customattribute: arr };
      },
    },
    formList: (settings: any) => {
      const { customattribute } = settings;
      // 处理自定义属性，注意只保存单选和单选
      const formCustomAttribute: any =
        customattribute?.map((item: any) => ({
          label: item.title,
          name: `${item.id}${item.title}`,
          type: 'select',
          multiple: item.component_type === 'MULTI_CHOICE',
          options:
            item.sub_titles?.map((v: any) => ({ value: v, label: v })) || [],
          onChange(e: any, form: FormInstance) {
            form.setFieldValue(
              `custom_attributeList`,
              cloneDeep(customattribute),
            ); //每次选择，给form 赋值，
          },
        })) || [];

      return [
        {
          type: 'input',
          label: '关键字',
          name: 'keyword',
          suffix: true,
        },
        {
          label: '供应商类别',
          name: 'supplier_identities',
          type: 'select',
          multiple: true,
          options: [
            { label: '父供应商', value: 'PARENT' },
            { label: '子供应商', value: 'CHILD' },
            { label: '普通供应商', value: 'NORMAL' },
          ],
        },
        {
          type: 'select',
          label: '供货组织',
          name: 'supply_ogs_ids',
          hidden: !settings?.enable_organization,
          normalize: (value) =>
            value
              ? typeof value === 'string' || typeof value === 'number'
                ? [value]
                : value
              : undefined,
          selectRequestParams: {
            url: '/erp-mdm/hxl.erp.org.find',
            postParams: { level: 3 },
            responseTrans(data) {
              const options: SelectType[] = data.map((item: any) => {
                const obj: SelectType = {
                  label: item.name,
                  value: item.id,
                };
                return obj;
              });
              return options;
            },
          },

          options: [],
        },
        {
          type: 'select',
          label: '供货主体',
          name: 'main_body_id',
          hidden: settings?.enable_organization,
          selectRequestParams: {
            url: '/erp-mdm/hxl.erp.suppliermainbody.find',
            responseTrans(data) {
              const options: SelectType[] = data.map((item: any) => {
                const obj: SelectType = {
                  label: item.name,
                  value: item.id,
                };
                return obj;
              });
              return options;
            },
          },
          options: [],
        },
        {
          type: 'select',
          label: '结算方式',
          name: 'settlement_type',
          options: [
            {
              label: '现结',
              value: 0,
            },
            {
              label: '月结',
              value: 1,
            },
            {
              label: '批结',
              value: 2,
            },
            {
              label: '货到付款',
              value: 3,
            },
          ],
        },
        ...formCustomAttribute,
        {
          type: 'checkbox',
          label: '',
          colon: false,
          name: 'filter_un_actived',
          options: [{ label: '仅显示启用供应商', value: 'filter_un_actived' }],
        },
        {
          type: 'input',
          name: 'custom_attributeList',
          hidden: true,
        },
      ];
    },
    initialValues: { filter_un_actived: 'filter_un_actived' },
    // 特殊的自定义请求前参数，params为form表单数据
    customPrevPost: (customPrevPost: any, customdata: any) => {
      const { custom_attributeList } = customPrevPost;
      // 注意多选和单选 “sub_titles” 都要传 数组
      const custom_attribute =
        custom_attributeList?.filter((item: any) => {
          const key = `${item.id}${item.title}`;
          if (customPrevPost[key]) {
            item.sub_titles =
              item.component_type === 'SINGLE_CHOICE'
                ? [customPrevPost[key]]
                : customPrevPost[key]; // 过滤出已选择的数组
            // 只返回 sub_titles 数组长度大于 0 的项
            return item.sub_titles.length > 0;
          }
          return false;
        }) || [];

      const data = {
        ...customPrevPost,
        custom_attribute:
          custom_attribute?.length > 0 ? custom_attribute : null, //没有值后不需要传空数组，防止报错
        filter_un_actived:
          !!customPrevPost.filter_un_actived?.includes('filter_un_actived'),
        custom_attributeList: null,
      };

      if (customdata.item_dept_id) {
        data.user_store_id =
          customdata?.leftSelectNodeData?.level == 1
            ? customdata?.leftSelectNodeData?.id
            : customdata?.leftSelectNodeData?.store_id;
        data.user_ids = customdata?.leftSelectNodeData?.all_user_ids;
      }
      if (customdata.category_ids) {
        data.category_ids = [customdata.category_ids];
      }

      return data;
    },
    withSelect: true,
    selectOptions: [
      {
        value: 1,
        label: '供应商分类',
        reqUrl: '/erp-mdm/hxl.erp.suppliercategory.find',
      },
      {
        value: 2,
        label: '采购组',
        reqUrl: '/erp/hxl.erp.purchasegroup.find',
      },
    ],
    leftKey: (url) => {
      const obj: any = {
        '/erp-mdm/hxl.erp.suppliercategory.find': {
          queryParams: 'category_ids',
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp/hxl.erp.purchasegroup.find': {
          dataType: 'tree',
          queryParams: 'item_dept_id',
          fieldName: {
            children: 'users',
            id: 'key',
          },
        },
      };

      return obj[url];
    },

    prevPost(params: any) {
      return {
        ...params,
        category_ids: params.category_ids ? [params.category_ids] : undefined,
        filter_un_actived: params.filter_un_actived?.includes(
          'filter_un_actived',
        )
          ? true
          : false,
      };
    },
  },
  merchant: {
    title: '选择商户',
    url: '/bms/bms.merchant.find',
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
      {
        type: 'select',
        label: '配送类型',
        name: 'merchants_type',
        options: [
          { label: '门店', value: 0 },
          { label: '供应商', value: 1 },
          { label: '批发客户', value: 2 },
        ],
      },
    ],
    columns: [
      {
        name: '商户编号',
        code: 'order_id',
        width: 180,
      },
      {
        name: '商户名称',
        code: 'name',
        width: 200,
      },
      {
        name: '类型',
        code: 'merchants_type',
        width: 100,
        render(text) {
          const obj: any = {
            0: '门店',
            1: '供应商',
            2: '批发客户',
          };
          return obj[text];
        },
      },
      {
        name: '营业执照名称',
        code: 'business_license_name',
        width: 180,
      },
      {
        name: '统一信用代码',
        code: 'social_credit_code',
        width: 160,
      },
      {
        name: '法人姓名',
        code: 'corp_name',
        width: 120,
      },
      {
        name: '',
        code: '',
      },
    ],

    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
    withSelect: true,
    selectOptions: [
      {
        value: 1,
        label: '供应商分类',
        reqUrl: '/erp-mdm/hxl.erp.suppliercategory.find',
      },
      {
        value: 2,
        label: '采购组',
        reqUrl: '/erp/hxl.erp.purchasegroup.find',
      },
    ],
    leftKey: (url) => {
      const obj: any = {
        '/erp-mdm/hxl.erp.suppliercategory.find': {
          queryParams: 'category_ids',
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp/hxl.erp.purchasegroup.find': {
          queryParams: 'item_dept_id',
          fieldName: { parent_id: 'parent_id' },
        },
      };

      return obj[url];
    },
    prevPost(params: any) {
      return {
        ...params,
        category_ids: params.category_ids ? [params.category_ids] : undefined,
      };
    },
  },
  // // 商品分类 应配置 formList下的 treeModalConfig
  // suppliercategory: {
  //   title: "选择商品分类",
  //   url: "/erp-mdm/hxl.erp.suppliercategory.find",
  //   columns: [
  //     {
  //       name: '商品分类名称',
  //       width: 650,
  //       code: 'name',
  //     },
  //     {
  //       name: '',
  //       width: 160,
  //     }
  //   ],
  //   formList: [
  //     {
  //       type: "input",
  //       label: "关键字",
  //       name: "name",
  //       suffix: true,
  //     },
  //   ]
  // },
  labour: {
    title: '选择劳务商',
    url: '/erp/hxl.erp.labour.page',
    columns: [
      {
        name: '代码',
        code: 'code',
        features: { sortable: true },
        width: 100,
      },
      {
        name: '名称',
        code: 'name',
        features: { sortable: true },
        width: 200,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
  },
  user: {
    title: '选择用户',
    url: '/erp-mdm/hxl.erp.user.page',
    columns: [
      {
        name: '用户名称',
        code: 'name',
        features: { sortable: true },
        width: 150,
      },
      {
        name: '所属门店',
        code: 'store_name',
        features: { sortable: false },
        width: 150,
      },
      {
        name: '用户角色',
        code: 'role_name',
        features: { sortable: false },
        width: 150,
      },
      {
        name: '业务部门',
        code: 'business_dept',
        features: { sortable: true },
        width: 100,
        render(text, record, index) {
          return userDept[text];
        },
      },
      {
        name: '是否启用',
        code: 'enabled',
        features: { sortable: true },
        width: 90,
        render(text, record, index) {
          return text ? '是' : '否';
        },
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
  },
  rosterUser: {
    title: '选择成员',
    url: '/kms/hxl.kms.user.page',
    columns: [
      {
        name: '序号',
        code: '_index',
      },
      {
        name: '名称',
        code: 'name',
        features: { sortable: true },
        width: 100,
      },
      {
        name: '联系方式',
        code: 'phone',
        features: { sortable: true },
        width: 100,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
      },
    ],
    fieldName: {
      parent_id: 'pid',
    },
  },
  storeHouse: {
    title: '选择仓库',
    url: '/erp/hxl.erp.storehouse.page',
    leftUrl: '/erp-mdm/hxl.erp.storegroup.find',
    leftKey: () => {
      return {
        queryParams: 'store_ids',
        isWithChild: true,
        fieldName: {
          parent_id: 'parent_id',
          // children: 'children'
          id: 'id',
          // name: 'store_name'
        },
      };
    },
    columns: [
      {
        name: '序号',
        code: '_index',
      },
      {
        name: '仓库代码',
        code: 'code',
        width: 130,
        features: { sortable: true },
      },
      {
        name: '仓库名称',
        code: 'name',
        width: 160,
        features: { sortable: true },
      },
      {
        name: '所属门店',
        code: 'store_name',
        width: 160,
        features: { sortable: true },
        render: (value: any, record: any, index: number) => {
          return <span>{record.store.store_name}</span>;
        },
      },
      {
        name: '启用',
        code: 'status',
        width: 70,
        features: { sortable: true },
        render: (text) => {
          return (
            <div>
              {text ? (
                <span className="info">{'是'}</span>
              ) : (
                <span className="info" style={{ color: 'red' }}>
                  {'否'}
                </span>
              )}
            </div>
          );
        },
      },
      {
        name: '默认仓库',
        code: 'default_flag',
        width: 100,
        features: { sortable: true },
        render: (text) => {
          return (
            <div>
              {text ? (
                <span className="info">{'是'}</span>
              ) : (
                <span className="info" style={{ color: 'red' }}>
                  {'否'}
                </span>
              )}
            </div>
          );
        },
      },
      {
        name: '配送仓',
        code: 'distribution',
        width: 100,
        features: { sortable: true },
        render: (text) => {
          return (
            <div>
              {text ? (
                <span className="info">{'是'}</span>
              ) : (
                <span className="info" style={{ color: 'red' }}>
                  {'否'}
                </span>
              )}
            </div>
          );
        },
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
      },
    ],
  },

  // 消费券名称
  coupon: {
    title: '选择消费券',
    url: '/erp/hxl.erp.coupontype.page',
    columns: [
      {
        name: '序号',
        code: '_index',
      },
      {
        name: '名称',
        code: 'name',
        features: { sortable: true },
        width: 240,
      },
      {
        name: '类型',
        code: 'type',
        features: { sortable: true },
        width: 120,
      },
      {
        name: '启用',
        code: 'status',
        width: 100,
        render: (text) => {
          return (
            <div>
              {text ? (
                <span className="info">{'是'}</span>
              ) : (
                <span className="info" style={{ color: 'red' }}>
                  {'否'}
                </span>
              )}
            </div>
          );
        },
      },
    ],
    settingProps: {
      url: '/erp/hxl.erp.baseparam.read',
    },
    formList: (settings, data) => {
      return [
        {
          type: 'input',
          label: '关键字',
          name: 'keyword',
          suffix: true,
        },
        {
          type: 'select',
          label: '门店标签',
          name: 'store_label_ids',
          multiple: true,
          options: [],
          selectRequestParams: {
            url: '/erp/hxl.erp.storelabel.find',
            responseTrans(data) {
              const options: SelectType[] = data.map((item: any) => {
                const obj: SelectType = {
                  label: item.store_label_name,
                  value: item.id,
                };
                return obj;
              });
              return options;
            },
          },
        },
        {
          type: 'select',
          label: '配送类型',
          name: 'delivery_type',
          options: [
            { label: '直营', value: 'DIRECT' },
            { label: '加盟', value: 'JOIN' },
          ],
        },
        {
          type: 'select',
          label: '经营类型',
          name: 'management_type',
          options: [
            { label: '直营店', value: '0' },
            { label: '加盟店', value: '1' },
          ],
        },
        {
          type: 'inputDialog',
          label: '行政区域',
          name: 'city_codes',
          treeModalConfig: {
            zIndex: 2002,
            title: '选择区域', // 标题
            url: '/erp-mdm/hxl.erp.store.area.find.all', // 请求地址
            dataType: 'lists',
            checkable: true, // 是否多选
            primaryKey: 'code',
            // data: {
            //   enabled: true
            // },
            // params: {
            //   company_id: LStorage.get('userInfo')?.company_id,
            //   // levels: [0, 1]
            // },
            // requestParams: {
            //   levels: [0, 1]
            // },
            fieldName: {
              id: 'code',
              parent_id: 'parent_code',
            },
            width: 360, // 模态框宽度
          },
          fieldNames: {
            // @ts-ignore
            idKey: 'code',
            nameKey: 'name',
          },
        },
        {
          type: 'inputDialog',
          label: '业务区域',
          name: 'business_area_ids',
          // normalize: (value, preValue, allValues) => {
          //   console.log(value, 'normalize', preValue, allValues)
          //   return value ? value : undefined
          // },
          dialogParams: {
            type: 'businessArea',
            dataType: 'lists',
            isLeftColumn: true,
            isMultiple: true,
            initAfterPost: (data: any) => {
              const dataValueDetails = data?.value_details?.map((v) => {
                return {
                  ...v,
                  show_name:
                    v.six_name ||
                    v.five_name ||
                    v.four_name ||
                    v.third_name ||
                    v.second_name ||
                    v.first_name,
                };
              });
              return {
                list: dataValueDetails,
                total: data?.value_details?.length,
              };
            },

            handleDefaultValue: (data) => {
              const _data = data.map((v) => {
                return {
                  ...v,
                  id: v.id,
                  show_name:
                    v.six_name ||
                    v.five_name ||
                    v.four_name ||
                    v.third_name ||
                    v.second_name ||
                    v.first_name,
                };
              });
              return _data;
            },
          },
          getValueFromEvent: (e: any, arr) => {
            let arrT: any[] = [];
            arr.forEach((h: any) => {
              arrT = [...arrT, ...h?.ids];
            });
            return arrT;
          },
          fieldNames: {
            idKey: 'id',
            nameKey: 'show_name',
          },
        },
        {
          type: 'compactDatePicker',
          label: '开业日期',
          name: 'opening_time',
          format: 'YYYY-MM-DD',
        },
        {
          type: 'select',
          label: '门店状态',
          name: 'states',
          width: 204,
          options: [
            { value: 'TO_BE_DELIVERED', label: '待交付' },
            { value: 'BUILDING', label: '营建中' },
            { value: 'TO_BE_OPENED', label: '待营业' },
            { value: 'ON_LEAVE', label: '休息中' },
            { value: 'OPENED', label: '营业中' },
            { value: 'CLOSED', label: '已闭店' },
            { value: 'CANCELLED', label: '已取消' },
            { value: 'TO_BE_CLOSED', label: '待停业' },
          ],
          normalize: (value) =>
            value
              ? typeof value === 'string' || typeof value === 'number'
                ? [value]
                : value
              : undefined,
        },
        {
          type: 'checkbox',
          label: '',
          name: 'wait_assign',
          colon: false,
          width: 300,
          hidden: !data?.isShowWaitAssign,
          options: [{ label: '仅查待分配门店', value: 'wait_assign' }],
        },
      ];
    },
    withSelect: true,
    selectOptions: (settings, data) => {
      const _org = data?.org_ids?.length
        ? [data?.org_ids?.[0] ? data?.org_ids?.[0] : 0]
        : [];
      return [
        {
          label: '门店分组',
          value: 'group',
          reqUrl: '/erp-mdm/hxl.erp.storegroup.find',
        },
        {
          label: '业务区域',
          value: 'business',
          reqUrl: '/erp-mdm/hxl.erp.businessarea.find',
          data: {
            org_ids: _org?.filter(Boolean),
            max_level: 3,
          },
        },
        {
          label: '行政区域',
          value: 'admin',
          reqUrl: '/erp-mdm/hxl.erp.store.area.find.all',
          filter: [
            {
              label: '省',
              value: 1,
            },
            {
              label: '市',
              value: 2,
            },
            {
              label: '区',
              value: 3,
            },
          ],
        },
        {
          label: '门店标签',
          value: 'tag',
          reqUrl: '/erp/hxl.erp.storelabel.find',
        },
        settings?.enable_organization && !data?.org_ids?.length
          ? {
              label: '组织分组',
              value: 'org',
              reqUrl: '/erp-mdm/hxl.erp.org.find',
            }
          : null,
      ].filter((item) => !!item) as Array<SelectTreeType>;
    },
    primaryKey: 'id',
    // leftKey: 'store_group_id',
    leftKey: (url: string): string => {
      const urlMatchLeftKey: Record<string, any> = {
        '/erp-mdm/hxl.erp.storegroup.find': {
          isWithChild: true,
          queryParams: 'store_group_ids',
        },
        '/erp-mdm/hxl.erp.businessarea.find': {
          // isWithChild: true,
          queryParams: 'business_area_ids',
        },
        '/erp-mdm/hxl.erp.store.area.find.all': {
          // isWithChild: true,
          queryParams: 'city_codes',
          fieldName: { parent_id: 'parent_code', id: 'code' },
        },
        '/erp/hxl.erp.storelabel.find': {
          // isWithChild: true,
          queryParams: 'store_label_ids',
          fieldName: { id: 'id', name: 'store_label_name' },
        },
        '/erp-mdm/hxl.erp.org.find': {
          // isWithChild: true,
          queryParams: 'org_ids',
          fieldName: { id: 'id', name: 'name' },
        },
      };
      return urlMatchLeftKey[url];
    },

    // @ts-ignore
    customPrevPost: (params: any, data: any) => {
      const result: any = {};
      Object.keys(data).forEach((key: any) => {
        const val = data[key];
        if (val) {
          result[key] =
            key == 'store_group_ids' ? data[key].filter(Boolean) : [data[key]];
        } else {
          result[key] = undefined;
        }
      });
      return {
        ...params,
        ...result,
        opening_time: params?.opening_time?.length
          ? params.opening_time
          : undefined,
        wait_assign: params?.wait_assign?.includes('wait_assign')
          ? true
          : false,
      };
    },
  },
  // 商品部门
  productDept: {
    title: '选择部门',
    url: '/erp-mdm/hxl.erp.dept.find',
    columns: [
      {
        name: '商品部门名称',
        width: 650,
        code: 'name',
      },
      {
        name: '',
        width: 160,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
  },
  // 批发客户
  wholesaler: {
    title: '选择批发客户',
    url: '/erp/hxl.erp.client.page',
    leftUrl: '/erp/hxl.erp.clientcategory.find',
    columns: [
      {
        name: '客户代码',
        code: 'code',
        width: 120,
      },
      {
        name: '批发客户名称',
        code: 'name',
        width: 180,
      },
      {
        name: '批发客户类别',
        code: 'client_category_name',
        width: 120,
        render: (_, record) => {
          return <div>{record?.client_category?.name || '-'}</div>;
        },
      },
      {
        name: '',
        code: '',
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
      // {
      //   type: 'select',
      //   label: '门店标签',
      //   name: 'store_label_ids',
      //   multiple: true,
      //   options: [],
      //   selectRequestParams: {
      //     url: '/erp/hxl.erp.storelabel.find',
      //     responseTrans(data) {
      //       const options: SelectType[] = data.map((item: any) => {
      //         const obj: SelectType = {
      //           label: item.store_label_name,
      //           value: item.id
      //         }
      //         return obj
      //       })
      //       return options
      //     }
      //   }
      // },
      // {
      //   type: 'select',
      //   label: '经营类型',
      //   name: 'management_type',
      //   options: [
      //     { label: '直营店', value: '0' },
      //     { label: '加盟店', value: '1' }
      //   ]
      // },
      // {
      //   type: 'select',
      //   label: '配送类型',
      //   name: 'delivery_type',
      //   options: [
      //     { label: '直营', value: 'DIRECT' },
      //     { label: '加盟', value: 'JOIN' }
      //   ]
      // }
    ],
    primaryKey: 'id',
    leftKey: 'category_ids',
    prevPost(params: any) {
      return {
        ...params,
        category_ids: Array.isArray(params.category_ids)
          ? params.category_ids
          : params.category_ids === 0
            ? []
            : params.category_ids != null
              ? [params.category_ids]
              : null,
      };
    },
    // fieldName: {
    //   parent_id: 'parent_id'
    // }
    // isWithChild: true
  },
  /**
   * @采购经理
   */
  purchaseManager: {
    title: '采购经理',
    url: '/scm/hxl.scm.itemapplyorder.userpurchasemanager.find',
    columns: [
      {
        name: '序号',
        code: '_index',
        width: 60,
      },
      {
        name: '用户名称',
        code: 'name',
        width: 180,
      },
      {
        name: '手机号',
        code: 'phone',
        width: 120,
        render: (text) => {
          return <div>{text?.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}</div>;
        },
      },
    ],
    formList: [
      {
        type: 'input',
        label: '用户名称',
        name: 'name',
        suffix: true,
      },
      {
        type: 'input',
        label: '手机号',
        name: 'phone',
        suffix: true,
      },
    ],
    primaryKey: 'id',
  },
  /**
   * @单据选择 待PromiseModal重构
   */
  // orderList: {
  //   title: '选择单据',
  //   url: '/scm/hxl.scm.labourfee.order.page',
  //   // leftUrl: '/erp-mdm/hxl.erp.storegroup.find',
  //   columns: [
  //     {
  //       name: '序号',
  //       code: '_index',
  //       width: 60,
  //       align: 'center',
  //       lock: true,
  //     },
  //     // {
  //     //   name: '单据号',
  //     //   code: 'fid',
  //     //   width: 200,
  //     //   features: { sortable: true },
  //     //   align: 'left',
  //     //   render: (value, record) => {
  //     //     return (
  //     //       <div
  //     //         className="link overwidth"
  //     //         onClick={(e) => {
  //     //           e.stopPropagation();
  //     //           NiceModal.show(PromiseModal, {
  //     //             order_type: record.order_type,
  //     //             order_fid: value,
  //     //           });
  //     //         }}
  //     //       >
  //     //         {value}
  //     //       </div>
  //     //     );
  //     //   },
  //     // },
  //     {
  //       name: '供应商代码',
  //       code: 'supplier_code',
  //       width: 120,
  //       features: { sortable: true },
  //       align: 'left',
  //     },
  //     {
  //       name: '供应商名称',
  //       code: 'supplier_name',
  //       width: 250,
  //       features: { sortable: true },
  //       align: 'left',
  //     },
  //     {
  //       name: '单据类型',
  //       code: 'order_type',
  //       width: 170,
  //       features: { sortable: true },
  //       align: 'left',
  //     },
  //     {
  //       name: '状态',
  //       code: 'state',
  //       width: 100,
  //       features: { sortable: true },
  //       align: 'left',
  //       render: (text) => {
  //         return text == 'INIT' ? '制单' : '审核';
  //       },
  //     },
  //     // {
  //     //   name: '金额(含税)',
  //     //   code: 'money',
  //     //   width: 134,
  //     //   features: { sortable: true },
  //     //   align: 'right'
  //     // },
  //     // {
  //     //   name: '数量',
  //     //   code: 'quantity',
  //     //   width: 110,
  //     //   features: { sortable: true },
  //     //   align: 'right'
  //     // },
  //     // {
  //     //   name: '商品数',
  //     //   code: 'item_count',
  //     //   width: 90,
  //     //   features: { sortable: true },
  //     //   align: 'right'
  //     // },
  //     {
  //       name: '制单人',
  //       code: 'create_by',
  //       width: 120,
  //       features: { sortable: true },
  //       align: 'left',
  //     },
  //     {
  //       name: '制单时间',
  //       code: 'create_time',
  //       width: 220,
  //       features: { sortable: true },
  //       align: 'left',
  //     },
  //     {
  //       name: '审核人',
  //       code: 'audit_by',
  //       width: 120,
  //       features: { sortable: true },
  //       align: 'left',
  //     },
  //     {
  //       name: '审核时间',
  //       code: 'audit_time',
  //       width: 220,
  //       features: { sortable: true },
  //       align: 'left',
  //     },
  //     {
  //       name: '备注',
  //       code: 'memo',
  //       width: 280,
  //       features: { sortable: true },
  //       align: 'left',
  //     },
  //   ],
  //   formList: [
  //     {
  //       type: 'compactDatePicker',
  //       label: '日期范围',
  //       name: 'create_date',
  //       allowClear: false,
  //       //width: 300,
  //       // @ts-ignore
  //       onChange: (value, selectType: 'week' | 'day' | 'month' | 'year') => {
  //         // selectType 左侧选取的类型
  //       },
  //     },
  //     {
  //       label: '单据类型',
  //       name: 'order_type',
  //       type: 'select',
  //       clear: false,
  //       check: true,
  //       options: [
  //         {
  //           label: '采购收货单',
  //           value: '收货单',
  //         },
  //         {
  //           label: '采购退货单',
  //           value: '退货单',
  //         },
  //       ],
  //     },
  //     {
  //       label: '单据状态',
  //       name: 'order_state',
  //       type: 'select',
  //       clear: true,
  //       check: true,
  //       options: [
  //         {
  //           label: '制单',
  //           value: 'INIT',
  //           type: 'info',
  //         },
  //         {
  //           label: '审核',
  //           value: 'AUDIT',
  //           type: 'warning',
  //         },
  //       ],
  //     },
  //     {
  //       label: '单据号',
  //       name: 'fid',
  //       type: 'input',
  //       clear: true,
  //       check: true,
  //     },
  //     {
  //       label: '门店',
  //       name: 'store_ids',
  //       type: 'inputDialog',
  //       dialogParams: {
  //         type: 'store',
  //         dataType: 'lists',
  //         isLeftColumn: true,
  //         isMultiple: true,
  //         primaryKey: 'id',
  //         data: {
  //           enabled: true,
  //         },
  //         fieldName: {
  //           id: 'id',
  //           name: 'store_name',
  //         },
  //       },
  //       fieldNames: {
  //         idKey: 'id',
  //         nameKey: 'store_name',
  //       } as any,
  //     },
  //     {
  //       label: '供应商',
  //       name: 'supplier_ids',
  //       type: 'inputDialog',
  //       dialogParams: {
  //         type: 'supplier',
  //         dataType: 'lists',
  //         isLeftColumn: true,
  //         isMultiple: true,
  //         primaryKey: 'id',
  //         data: {
  //           enabled: true,
  //         },
  //       },
  //     },
  //   ],
  //   primaryKey: 'fid',
  //   //leftKey: 'store_group_ids',
  //   // fieldName: {
  //   //   parent_id: 'parent_id'
  //   // },
  //   isWithChild: true,
  // },

  /**
   * @费用选择
   */
  feeProject: {
    title: '选择费用',
    url: '/erp/hxl.erp.feeitem.find',
    // leftUrl: '/erp-mdm/hxl.erp.storegroup.find',
    columns: [
      {
        name: '序号',
        code: '_index',
        width: 60,
        align: 'center',
      },
      {
        name: '费用项目名称',
        code: 'name',
        width: 160,
        features: { sortable: true },
      },
      {
        name: '收支方向',
        code: 'flag',
        width: 120,
        // features: { sortable: true }
        render: (text, record, index) => {
          return text ? '应收' : '应付';
        },
      },
      {
        name: '费用项目类型',
        code: 'item_type',
        width: 120,
        features: { sortable: true },
        render: (text) => {
          return text == 'CHANGE' ? '变动费用' : '固定费用';
        },
      },
      {
        name: '开票属性',
        code: 'account_type',
        width: 120,
        render: (text, record, index) => {
          return accountType.find((v) => v.value === text)?.label || '';
        },
        // features: { sortable: true }
      },
      {
        name: '内置费用项目',
        code: 'default_flag',
        width: 120,
        render: (text) => {
          return (
            <div>
              {text ? (
                <span className="info">{'是'}</span>
              ) : (
                <span className="info" style={{ color: 'red' }}>
                  {'否'}
                </span>
              )}
            </div>
          );
        },
        // features: { sortable: true }
      },
      {
        name: '',
        // width: 180
      },
    ],
    formList: [
      {
        label: '关键字',
        name: 'keyword',
        type: 'input',
        clear: true,
        check: true,
        // placeholder:'名称'
      },
    ],
    primaryKey: 'id',
    //leftKey: 'store_group_ids',
    // fieldName: {
    //   parent_id: 'parent_id'
    // },
    isWithChild: true,
  },

  /**
   * name @供应商合同添加商品
   */

  // 商品档案
  supplierContractItem: {
    title: '选择商品',
    url: '/erp-mdm/hxl.erp.item.short.page',
    leftUrl: '/erp-mdm/hxl.erp.category.find',
    resetForm: true,
    columns: [
      {
        name: '代码',
        code: 'code',
        width: 80,
      },
      {
        name: '代码',
        code: 'bar_code',
        width: 80,
      },
      {
        name: '商品名称',
        code: 'name',
        width: 150,
        features: {
          sortable: true,
        },
      },
      {
        name: '采购规格',
        code: 'purchase_spec',
        width: 104,
      },
      {
        name: '商品类型',
        code: 'item_type',
        width: 80,
        render(text) {
          const obj: any = {
            STANDARD: '标准商品',
            COMBINATION: '组合商品',
            COMPONENT: '成分商品',
            MAKEBILL: '制单组合',
            GRADING: '分级商品',
          };
          return obj[text];
        },
      },
      {
        name: '基本单位',
        code: 'unit',
        width: 80,
      },
      {
        name: '保质期',
        code: 'period',
        width: 80,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
      {
        type: 'select',
        label: '商品标签',
        name: 'item_label_id',
        selectRequestParams: {
          url: '/erp-mdm/hxl.erp.itemlabel.find',
          responseTrans(data) {
            const options: SelectType[] = data.map((item: any) => {
              const obj: SelectType = {
                label: item.item_label_name,
                value: item.id,
              };
              return obj;
            });
            return options;
          },
        },
        // @ts-ignore
        onChange(e, form: FormInstance) {
          if (!e) {
            form.setFieldValue('item_label_ids', []);
          } else {
            form.setFieldValue('item_label_ids', [e]);
          }
        },
        options: [],
      },
      {
        type: 'inputPanel',
        label: '商品属性',
        name: 'checkValue',
        placeholder: '正常/停购/停售/停止要货/停止批发',
        afterChange: (value) => {
          // 正常与四个停止选项互斥逻辑
          const stopArr = [
            'stop_purchase',
            'stop_sale',
            'stop_request',
            'stop_wholesale',
          ];
          const stopMap: Record<string, number> = {
            normal: -1,
            stop_purchase: -1,
            stop_sale: -1,
            stop_request: -1,
            stop_wholesale: -1,
          };
          value.forEach((item, index) => {
            const wight = stopMap[item];
            if (typeof wight === 'number') {
              stopMap[item] = index;
            }
          });

          let wight: number = -1,
            current: string,
            newValue: typeof value;
          Object.keys(stopMap).forEach((key) => {
            const value = stopMap[key];
            if (wight < value) {
              wight = value;
              current = key;
            }
          });

          if (current == 'normal') {
            newValue = value.filter((item) => !stopArr.includes(item));
          } else {
            newValue = value.filter((item) => item !== 'normal');
          }

          return newValue;
        },
        items: [
          {
            label: '仅显示',
            key: 1,
          },
          {
            label: '不显示',
            key: 0,
          },
        ],
        allowClear: true,
        options: itemProperty,
        width: 372,
      },
      {
        type: 'input',
        name: 'item_label_ids',
        hidden: true,
      },
    ],
    prevPost: (params: any) => {
      const data = {
        ...params,
      };
      itemProperty.forEach((item) => {
        data[item.value] = undefined;
      });
      params?.checkValue?.forEach((v) => {
        data[v.value] = !!v.itemKey;
      });
      return data;
    },
    // initialValues: {
    //   checkValue: [
    //     // { label: "正常", value: "normal", itemLable: "仅显示", itemKey: 1 }
    //     'normal'
    //   ]
    // },
    withSelect: true,
    selectOptions: [
      {
        value: 1,
        label: '商品分类',
        reqUrl: '/erp-mdm/hxl.erp.category.find',
      },
      {
        value: 2,
        label: '商品部门',
        reqUrl: '/erp-mdm/hxl.erp.dept.find',
      },
      {
        value: 3,
        label: '商品品牌',
        reqUrl: '/erp-mdm/hxl.erp.brand.find',
      },
      {
        value: 4,
        label: '供应商',
        reqUrl: '/erp-mdm/hxl.erp.suppliercategory.findwithsupplier',
      },
    ],
    leftKey: (url) => {
      const obj: any = {
        '/erp-mdm/hxl.erp.category.find': {
          queryParams: 'category_ids',
          isWithChild: true,
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp-mdm/hxl.erp.dept.find': {
          queryParams: 'item_dept_id',
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp-mdm/hxl.erp.brand.find': {
          queryParams: 'item_brand_id',
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp-mdm/hxl.erp.suppliercategory.findwithsupplier': {
          queryParams: 'supplier_ids',
          isWithChild: true,
          fieldName: { parent_id: 'parent_id', children: 'suppliers' },
          dataType: 'tree',
        },
      };

      return obj[url];
    },
  },
  /**
   * @经营范围
   */
  businessRange: {
    title: '选择经营范围',
    url: '/erp-mdm/hxl.erp.businessscope.short.find',
    // leftUrl: '/center/hxl.center.businessscopecategory.find',
    // withSelect: true,
    // leftKey: 'category_id',
    // selectOptions: [
    //   {
    //     value: 0,
    //     label: '采购经营范围',
    //     reqUrl: '/center/hxl.center.businessscopecategory.find',
    //     requestParams: { business_type: '0' },
    //   },
    // ],
    columns: [
      {
        name: '序号',
        code: '_index',
        align: 'center',
        width: 50,
      },
      {
        name: '经营范围名称',
        code: 'name',
        features: { sortable: true },
        width: 200,
      },
      {
        name: '类别',
        code: 'category_name',
        features: { sortable: true },
        width: 200,
      },
      {
        name: '组织',
        code: 'organizations',
        features: { sortable: true },
        width: 200,
        render(text, record) {
          return Array.isArray(record.organizations) &&
            record?.organizations?.length
            ? record?.organizations?.map((v) => v?.name)
            : '';
        },
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
      },
    ],
  },

  // 经营范围加组织+类别
  businessRangeOrg: {
    title: '选择经营范围',
    url: '/erp-mdm/hxl.erp.businessscope.short.find',
    leftUrl: '/erp-mdm/hxl.erp.org.find',
    leftKey: () => {
      return {
        queryParams: 'org_ids',
        isWithChild: true,
        fieldName: {
          parent_id: 'parent_id',
          // children: 'children'
          id: 'id',
          // name: 'store_name'
        },
      };
    },
    columns: [
      {
        name: '序号',
        code: '_index',
        align: 'center',
        width: 50,
      },
      {
        name: '经营范围名称',
        code: 'name',
        features: { sortable: true },
        width: 200,
      },
      {
        name: '类别',
        code: 'category_name',
        features: { sortable: true },
        width: 200,
      },
      {
        name: '组织',
        code: 'organizations',
        features: { sortable: true },
        width: 200,
        render(text, record) {
          return Array.isArray(record.organizations) &&
            record?.organizations?.length
            ? record?.organizations?.map((v) => v?.name)
            : '';
        },
      },
    ],
  },
  //经营范围Short
  businessRangeShort: {
    title: '选择经营范围',
    url: '/erp-mdm/hxl.erp.businessscope.find',
    columns: (settings, data) => {
      return [
        {
          name: '经营范围名称',
          code: 'name',
          features: { sortable: true },
          width: 200,
        },
        {
          name: '类别',
          code: 'category_name',
          features: { sortable: true },
          width: 200,
        },
        {
          name: '组织',
          code: 'organizations',
          features: { sortable: true },
          width: 200,
          hidden: !data?.enable_organization,
          render: (text, record) => {
            return record?.organizations?.map((v) => v?.name);
          },
        },
      ];
    },
    // settingProps: {
    //   url: '/erp/hxl.erp.baseparam.read'
    // },
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
      },
    ],
  },
  /**
   * @商品档案经营范围
   */
  businessRecordRange: {
    title: '选择经营范围',
    url: '/erp-mdm/hxl.erp.businessscope.short.find',
    columns: [
      {
        name: '序号',
        code: '_index',
        align: 'center',
        width: 50,
      },
      {
        name: '经营范围名称',
        code: 'name',
        features: { sortable: true },
        width: 200,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
      },
    ],
  },

  /**
   * @ scm仓库抽检 入库申请单单据选择
   */
  scmInOrder: {
    title: '选择单据',
    url: '/wms/hxl.wms.inapplicationorder.scm.find',
    columns: [
      {
        name: '序号',
        code: '_index',
        width: 60,
        align: 'center',
        lock: true,
      },
      {
        name: '单据号',
        code: 'fid',
        width: 200,
        features: { sortable: true },
      },
      {
        name: '供应商名称',
        code: 'client_id',
        width: 250,
        features: { sortable: true },
        render(text, record) {
          return record?.client_name;
        },
      },
      {
        name: '预约时间',
        code: 'appointment_date',
        width: 150,
        features: { sortable: true },
      },
    ],
    formList: [
      {
        type: 'compactDatePicker',
        label: '日期范围',
        name: 'audit_date',
        allowClear: false,
      },
      {
        label: '单据号',
        name: 'keyword',
        type: 'input',
      },
    ],
    initialValues: {
      audit_date: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    },
    primaryKey: 'fid',
  },
  //#region 选择操作员
  wmsOperator: {
    title: '选择操作员',
    url: '/erp-mdm/hxl.erp.user.page',
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
    columns: [
      {
        title: '操作员名称',
        code: 'name',
        width: 240,
        features: { sortable: true },
      },
    ],
  },
  //#region 选择用户
  wmsUser: {
    title: '选择用户',
    url: '/erp-mdm/hxl.erp.user.page',
    columns: [
      {
        name: '用户名称',
        code: 'name',
        features: { sortable: true },
        width: 100,
      },
      {
        name: '所属门店',
        code: 'store_name',
        features: { sortable: true },
        width: 120,
      },
      {
        name: '用户角色',
        code: 'role_name',
        features: { sortable: true },
        width: 120,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
  },

  // 商品部门
  storeLabel: {
    title: '选择门店标签',
    url: '/erp/hxl.erp.storelabel.find',
    columns: [
      {
        name: '门店标签名称',
        width: 650,
        code: 'store_label_name',
      },
      {
        name: '',
        width: 160,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
      {
        type: 'select',
        multiple: true,
        label: '应用用户部门',
        name: 'user_dept_ids',
        options: [],
        selectRequestParams: {
          url: '/erp-mdm/hxl.erp.userdept.find',
          responseTrans(data) {
            const options: SelectType[] = data.map((item: any) => {
              const obj: SelectType = {
                label: item.store_label_name,
                value: item.id,
              };
              return obj;
            });
            return options;
          },
        },
      },
    ],
  },
  // 采购计划
  purchasePlan: {
    url: '/erp/hxl.erp.purchaseplan.page ',
    title: '选择采购计划',
    initialValues: {
      enableSelect: ['enable'],
    },
    formList: [
      {
        label: '关键字',
        name: 'keyword',
        type: 'input',
        allowClear: true,
      },
      {
        label: '所属组织',
        name: 'org_ids',
        type: 'select',
        allowClear: true,
        mode: 'multiple',
        selectRequestParams: {
          url: '/erp-mdm/hxl.erp.org.find',
          responseTrans(response) {
            return response.map((item: any) => {
              return {
                label: item.name,
                value: item.id,
              };
            });
          },
        },
      },
      {
        label: '',
        name: 'enableSelect',
        type: 'checkbox',
        colon: false,
        options: [{ label: '默认展示效期内的采购计划', value: 'enable' }],
      },
      {
        label: '按年份',
        name: 'year_date',
        type: 'datePicker',
        select_type: 'year',
        normalize: (value) => {
          return value ? value : undefined;
        },
      },
    ],
    columns: [
      {
        code: '_index',
        name: '序号',
      },
      {
        code: 'name',
        name: '计划名称',
        features: { sortable: true },
      },
      {
        code: 'org_name',
        name: '所属组织',
        features: { sortable: true },
      },
      {
        code: 'quantity',
        name: '预测数量',
        align: 'right',
        width: 160,
        features: { sortable: true, format: 'QUANTITY' },
      },
      {
        code: 'money',
        name: '预测金额（元）',
        width: 160,
        align: 'right',
        features: { sortable: true, format: 'MONEY' },
      },
    ],
    customPrevPost(params, data) {
      const { enableSelect } = params;
      if (Array.isArray(enableSelect) && enableSelect.length) {
        params.enable = true;
      } else {
        params.enable = false;
      }
      return params;
    },
  },
  /**
   * @补货模板
   */
  replenishTemplate: {
    title: '选择补货模板',
    url: '/erp/hxl.erp.replenishtemplate.find',
    leftUrl: '/erp-mdm/hxl.erp.org.find',
    columns: [
      {
        name: '序号',
        code: '_index',
        align: 'center',
        width: 50,
      },
      {
        name: '补货模板名称',
        code: 'name',
        width: 200,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
      },
    ],
  },
  selectBatchTemplate: {
    title: '选择批次', // 标题
    url: '/erp/hxl.erp.deliveryspecialprice.item.batch', // 请求地址
    columns: [
      {
        name: '生产日期',
        code: 'producing_date',
        width: 130,
      },
      {
        name: '保质期',
        code: 'expire_type_num',
        width: 130,
      },
      {
        name: '到期日期',
        code: 'expire_date',
        width: 130,
      },
      {
        name: '数量',
        code: 'quantity',
        width: 130,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
    tableProps: {
      hideOnSinglePage: true,
    },
  },
  basketPreparation: {
    title: '选择物资载具',
    url: '/erp/hxl.erp.basketorder.stock.find',
    columns: [
      {
        name: '物资载具名称',
        code: 'basket_name',
        width: 200,
      },
      {
        name: '单价',
        code: 'price',
        width: 120,
        render: (value) => {
          return `${value?.toFixed(4)}`;
        },
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
  },
  //#region 领用部门
  erpcollectDept: {
    title: '选择领用部门',
    url: '/erp-mdm/hxl.erp.userdept.find',
    columns: [
      {
        name: '用户部门名称',
        code: 'name',
        width: 150,
      },
      {
        name: '部门负责人',
        code: 'leader',
        width: 150,
      },
    ],
  },
  //#region 商品部门 1
  productBrand: {
    title: '选择商品品牌',
    url: '/erp-mdm/hxl.erp.brand.find',
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
    columns: [
      // {
      //   title: 'ID',
      //   key: 'id',
      //   dataIndex: 'id',
      //   ellipsis: true,
      //   sort: 'custom',
      //   width: 200
      // },
      {
        title: '品牌名称',
        code: 'name',
        dataIndex: 'name',
        ellipsis: true,
        sort: 'custom',
        width: 200,
      },
      {
        title: '',
        key: '',
        dataIndex: '',
      },
    ],
  },

  //#region 货主
  cargoOwner: {
    title: '选择货主',
    url: '/erp-mdm/hxl.erp.cargo.owner.pageforinner',
    columns: [
      {
        name: '序号',
        code: '_index',
        width: 60,
        align: 'center',
      },
      {
        name: '货主代码',
        code: 'owner_code',
        align: 'left',
        width: 130,
        features: { sortable: true },
      },
      {
        name: '组织代码/供应商代码',
        code: 'source_code',
        align: 'left',
        width: 165,
        features: { sortable: true },
      },
      {
        name: '组织名称/供应商名称',
        code: 'source_name',
        align: 'left',
        width: 165,
        features: { sortable: true },
      },
      {
        name: '货主类型',
        code: 'owner_type',
        align: 'left',
        width: 110,
        features: { sortable: true },
        render: (value: string) => {
          const curT = cargoList?.find((v) => v.value === value);
          return <div>{curT?.label}</div>;
        },
      },
      // {
      //   name: '创建人',
      //   code: 'create_by',
      //   align: 'left',
      //   width: 130,
      //   features: { sortable: true },
      // },
      // {
      //   name: '创建时间',
      //   code: 'create_time',
      //   align: 'left',
      //   width: 150,
      //   features: { sortable: true, format: 'TIME' },
      // },
    ],
    formList: (settings, data) => {
      return [
        {
          label: '关键字',
          name: 'keyword',
          type: 'input',
          allowClear: true,
          check: true,
        },
        {
          label: '货主类型',
          name: 'owner_type',
          type: 'select',
          check: true,
          defaultValue: data?.filterSupplier ? 'ORGANIZATION' : '',
          // multiple: true,
          disabled: data?.filterSupplier,
          allowClear: true,
          options: cargoList,
        },
      ];
    },
  },
  //#region 查询门店下的货主
  cargoOwnerStore: {
    title: '选择货主',
    url: '/erp/hxl.erp.delivery.cargo.owner.conf.readbystoreIds',
    columns: [
      {
        name: '序号',
        code: '_index',
        width: 60,
        align: 'center',
      },
      {
        name: '货主代码',
        code: 'owner_code',
        align: 'left',
        width: 180,
        features: { sortable: true },
      },
      {
        name: '组织代码/供应商代码',
        code: 'source_code',
        align: 'left',
        width: 180,
        features: { sortable: true },
      },
      {
        name: '组织名称/供应商名称',
        code: 'source_name',
        align: 'left',
        width: 180,
        features: { sortable: true },
      },
      {
        name: '货主类型',
        code: 'owner_type',
        align: 'left',
        width: 250,
        features: { sortable: true },
        render: (value: string) => {
          const curT = cargoList?.find((v) => v.value === value);
          return <div>{curT?.label}</div>;
        },
      },
      {
        name: '创建人',
        code: 'create_by',
        align: 'left',
        width: 400,
        features: { sortable: true },
      },
      {
        name: '创建时间',
        code: 'create_time',
        align: 'left',
        width: 162,
        features: { sortable: true, format: 'TIME' },
      },
    ],
    formList: [
      {
        label: '关键字',
        name: 'keyword',
        type: 'input',
        allowClear: true,
        check: true,
      },
      {
        label: '货主类型',
        name: 'owner_type',
        type: 'select',
        check: true,
        // multiple: true,
        allowClear: true,
        options: cargoList,
      },
    ],
    afterPost(data: any) {
      const dataValueDetails = data?.[0]?.cargo_owner_info_list?.map((v) => {
        return {
          ...v,
        };
      });
      return dataValueDetails || [];
    },
    fieldName: {
      idKey: 'id',
      nameKey: 'source_name',
    },
  },

  // 采购补货分析商品档案
  purchaseReplenishGoods: {
    title: '选择商品',
    url: '/erp/hxl.erp.purchasereplenishanalysis.item.page',
    leftUrl: '/erp/hxl.erp.category.find',
    resetForm: true,
    columns: [
      {
        name: '商品代码',
        code: 'code',
        width: 80,
      },
      {
        name: '商品条码',
        code: 'bar_code',
        width: 80,
      },
      {
        name: '商品名称',
        code: 'name',
        width: 150,
        features: {
          sortable: true,
        },
      },
      {
        name: '采购规格',
        code: 'purchase_spec',
        width: 104,
      },
      {
        name: '商品类型',
        code: 'item_type',
        width: 80,
        render(text) {
          const obj: any = {
            MAINSPEC: '主规格商品',
            MULTIPLESPEC: '多规格商品',
            STANDARD: '标准商品',
            COMBINATION: '组合商品',
            COMPONENT: '成分商品',
            MAKEBILL: '制单组合',
            GRADING: '分级商品',
          };
          return obj[text];
        },
      },
      {
        name: '基本单位',
        code: 'unit',
        width: 80,
      },
      {
        name: '保质期',
        code: 'period',
        width: 80,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
      {
        type: 'select',
        label: '商品标签',
        name: 'item_label_id',
        selectRequestParams: {
          url: '/erp/hxl.erp.itemlabel.find',
          responseTrans(data) {
            const options: SelectType[] = data?.map((item: any) => {
              const obj: SelectType = {
                label: item.item_label_name,
                value: item.id,
              };
              return obj;
            });
            return options;
          },
        },
        // @ts-ignore
        onChange(e, form: FormInstance) {
          if (!e) {
            form.setFieldValue('item_label_ids', []);
          } else {
            form.setFieldValue('item_label_ids', [e]);
          }
        },
        options: [],
      },
      {
        type: 'checkbox',
        label: '',
        colon: false,
        initialValue: ['normal'],
        name: 'normal',
        options: [{ label: '仅显示正常品', value: 'normal' }],
      },
    ],
    withSelect: true,
    selectOptions: [
      {
        value: 1,
        label: '商品分类',
        reqUrl: '/erp/hxl.erp.category.find',
      },
      {
        value: 2,
        label: '商品部门',
        reqUrl: '/erp/hxl.erp.dept.find',
      },
      {
        value: 3,
        label: '商品品牌',
        reqUrl: '/erp/hxl.erp.brand.find',
      },
      {
        value: 4,
        label: '供应商',
        reqUrl: '/erp-mdm/hxl.erp.suppliercategory.findwithsupplier',
      },
    ],
    leftKey: (url) => {
      const obj: any = {
        '/erp/hxl.erp.category.find': {
          queryParams: 'category_ids',
          isWithChild: true,
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp/hxl.erp.dept.find': {
          queryParams: 'item_dept_id',
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp/hxl.erp.brand.find': {
          queryParams: 'item_brand_id',
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp-mdm/hxl.erp.suppliercategory.findwithsupplier': {
          queryParams: 'supplier_ids',
          isWithChild: true,
          fieldName: { parent_id: 'parent_id', children: 'suppliers' },
          dataType: 'tree',
        },
      };
      return obj[url];
    },
    customPrevPost: (params: any) => {
      return {
        ...params,
        normal: params?.normal?.includes('normal'),
      };
    },
  },
  storeAdjustReasons: {
    title: '选择调整原因',
    url: '/erp/hxl.erp.stockadjustmentreason.find',
    // returnKey: 'list',
    columns: [
      {
        name: '库存调整原因名称',
        code: 'name',
        width: 180,
      },
      {
        name: '出入库标记',
        code: 'flag',
        width: 100,
        render: (value) => {
          return <div>{value ? '出库' : '入库'}</div>;
        },
      },
    ],
  },
  goods: {
    title: '选择商品',
    url: '/erp/hxl.erp.item.short.page',
    leftUrl: '/erp/hxl.erp.category.find',
    selectedAllUrl: '/erp/hxl.erp.item.short.ids.find',
    resetForm: true,
    columns: (setting, data = {}) => {
      return [
        {
          name: '代码',
          code: 'code',
          width: 80,
        },
        {
          name: '条码',
          code: 'bar_code',
          width: 80,
        },
        {
          name: '商品名称',
          code: 'name',
          width: 150,
          features: {
            sortable: true,
          },
        },
        {
          name: '采购规格',
          code: 'purchase_spec',
          width: 104,
        },
        {
          name: '7日均销',
          code: 'order7_day_average_sale_stock',
          width: 120,
          align: 'right',
          features: { format: 'QUANTITY' },
          hidden: !data?.isShowStoreOrders,
          render(text) {
            return text || '0.00';
          },
        },
        {
          name: '库存',
          code: 'stock_quantity',
          width: 120,
          align: 'right',
          features: { format: 'QUANTITY' },
          hidden: !data?.isShowStoreOrders,
        },
        {
          name: '商品类型',
          code: 'item_type',
          width: 80,
          render(text) {
            const obj: any = {
              MAINSPEC: '主规格商品',
              MULTIPLESPEC: '多规格商品',
              STANDARD: '标准商品',
              COMBINATION: '组合商品',
              COMPONENT: '成分商品',
              MAKEBILL: '制单组合',
              GRADING: '分级商品',
            };
            return obj[text];
          },
        },
        {
          name: data?.unit_type === 'WHOLESALE' ? '批发单位' : '基本单位',
          code: data?.unit_type === 'WHOLESALE' ? 'wholesale_unit' : 'unit',
          width: 80,
        },
        {
          name: '保质期',
          code: 'period',
          width: 80,
        },
      ];
    },
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
      {
        type: 'select',
        label: '商品标签',
        name: 'item_label_id',
        selectRequestParams: {
          url: '/erp/hxl.erp.itemlabel.find',
          responseTrans(data) {
            const options: SelectType[] = data.map((item: any) => {
              const obj: SelectType = {
                label: item.item_label_name,
                value: item.id,
              };
              return obj;
            });
            return options;
          },
        },
        // @ts-ignore
        onChange(e, form: FormInstance) {
          if (!e) {
            form.setFieldValue('item_label_ids', []);
          } else {
            form.setFieldValue('item_label_ids', [e]);
          }
        },
        options: [],
      },
      {
        type: 'input',
        name: 'item_label_ids',
        hidden: true,
      },
    ],
    withSelect: true,
    selectOptions: [
      {
        value: 1,
        label: '商品分类',
        reqUrl: '/erp/hxl.erp.category.find',
      },
      {
        value: 2,
        label: '商品部门',
        reqUrl: '/erp/hxl.erp.dept.find',
      },
      {
        value: 3,
        label: '商品品牌',
        reqUrl: '/erp/hxl.erp.brand.find',
      },
      {
        value: 4,
        label: '供应商',
        reqUrl: '/erp-mdm/hxl.erp.suppliercategory.findwithsupplier',
      },
    ],
    leftKey: (url) => {
      const obj: any = {
        '/erp/hxl.erp.category.find': {
          queryParams: 'category_ids',
          isWithChild: true,
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp/hxl.erp.dept.find': {
          queryParams: 'item_dept_id',
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp/hxl.erp.brand.find': {
          queryParams: 'item_brand_id',
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp-mdm/hxl.erp.suppliercategory.findwithsupplier': {
          queryParams: 'supplier_ids',
          isWithChild: true,
          fieldName: { parent_id: 'parent_id', children: 'suppliers' },
          dataType: 'tree',
        },
      };

      return obj[url];
    },
  },

  //
  //********* */
  // #region 组织 商品档案

  orgGoodsList: {
    title: '选择商品',
    url: '/erp-mdm/hxl.erp.org.item.page', // erp/hxl.erp.org.item.page
    leftUrl: '/erp/hxl.erp.category.find',
    resetForm: true,
    columns: [
      {
        name: '组织',
        code: 'org_name',
        width: 100,
      },
      {
        name: '代码',
        code: 'code',
        width: 80,
      },
      {
        name: '条码',
        code: 'bar_code',
        width: 80,
      },
      {
        name: '商品名称',
        code: 'name',
        width: 150,
        features: {
          sortable: true,
        },
      },
      {
        name: '采购规格',
        code: 'purchase_spec',
        width: 104,
      },
      {
        name: '商品类型',
        code: 'item_type',
        width: 80,
        render(text) {
          const obj: any = {
            MAINSPEC: '主规格商品',
            MULTIPLESPEC: '多规格商品',
            STANDARD: '标准商品',
            COMBINATION: '组合商品',
            COMPONENT: '成分商品',
            MAKEBILL: '制单组合',
            GRADING: '分级商品',
          };
          return obj[text];
        },
      },
      {
        name: '基本单位',
        code: 'unit',
        width: 80,
      },
      {
        name: '保质期',
        code: 'period',
        width: 80,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
      {
        type: 'select',
        label: '商品标签',
        name: 'item_label_id',
        selectRequestParams: {
          url: '/erp/hxl.erp.itemlabel.find',
          responseTrans(data) {
            const options: SelectType[] = data.map((item: any) => {
              const obj: SelectType = {
                label: item.item_label_name,
                value: item.id,
              };
              return obj;
            });
            return options;
          },
        },
        // @ts-ignore
        onChange(e, form: FormInstance) {
          if (!e) {
            form.setFieldValue('item_label_ids', []);
          } else {
            form.setFieldValue('item_label_ids', [e]);
          }
        },
        options: [],
      },

      {
        type: 'select',
        label: '组织',
        name: 'org_id',
        selectRequestParams: {
          url: '/erp-mdm/hxl.erp.org.find',
          postParams: {
            level: 2,
          },
          responseTrans(data) {
            const options: SelectType[] = data.map((item: any) => {
              const obj: SelectType = {
                label: item.name,
                value: item.id,
              };
              return obj;
            });
            return options;
          },
        },
        // @ts-ignore
        onChange(e, form: FormInstance) {
          if (!e) {
            form.setFieldValue('org_ids', []);
          } else {
            form.setFieldValue('org_ids', [e]);
          }
        },
        options: [],
      },
      {
        type: 'input',
        name: 'item_label_ids',
        hidden: true,
      },
    ],
    withSelect: true,
    selectOptions: [
      {
        value: 1,
        label: '商品分类',
        reqUrl: '/erp/hxl.erp.category.find',
      },
      {
        value: 2,
        label: '商品部门',
        reqUrl: '/erp/hxl.erp.dept.find',
      },
      {
        value: 3,
        label: '商品品牌',
        reqUrl: '/erp/hxl.erp.brand.find',
      },
      {
        value: 4,
        label: '供应商',
        reqUrl: '/erp-mdm/hxl.erp.suppliercategory.findwithsupplier',
      },
    ],
    leftKey: (url) => {
      const obj: any = {
        '/erp/hxl.erp.category.find': {
          queryParams: 'category_ids',
          isWithChild: true,
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp/hxl.erp.dept.find': {
          queryParams: 'item_dept_id',
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp/hxl.erp.brand.find': {
          queryParams: 'item_brand_id',
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp-mdm/hxl.erp.suppliercategory.findwithsupplier': {
          queryParams: 'supplier_ids',
          isWithChild: true,
          fieldName: { parent_id: 'parent_id', children: 'suppliers' },
          dataType: 'tree',
        },
      };

      return obj[url];
    },
  },

  cargoOwnerEdit: {
    title: '选择货主',
    url: '/erp-mdm/hxl.erp.cargo.owner.page',
    columns: (setting, data, change) => {
      console.log(setting, data, change);
      return [
        {
          name: '序号',
          code: '_index',
          width: 60,
          align: 'center',
        },
        {
          name: '货主代码',
          code: 'owner_code',
          align: 'left',
          width: 130,
          features: { sortable: true },
        },
        {
          name: '组织代码/供应商代码',
          code: 'source_code',
          align: 'left',
          width: 175,
          features: { sortable: true },
        },
        {
          name: '组织名称/供应商名称',
          code: 'source_name',
          align: 'left',
          width: 245,
          features: { sortable: true },
        },
        {
          name: '货主类型',
          code: 'owner_type',
          align: 'left',
          width: 110,
          features: { sortable: true },
          render: (value: string) => {
            const curT = cargoList?.find((v) => v.value === value);
            return <div>{curT?.label}</div>;
          },
        },
      ];
    },
    formList: [
      {
        label: '关键字',
        name: 'keyword',
        type: 'input',
        allowClear: true,
        check: true,
      },
      {
        label: '货主类型',
        name: 'owner_type',
        type: 'select',
        check: true,
        // multiple: true,
        allowClear: true,
        options: cargoList,
      },
    ],
  },
  producer: {
    title: '选择生产商',
    url: '/erp-mdm/hxl.erp.supplier.producer.find',
    columns: [
      {
        name: '序号',
        code: '_index',
        width: 60,
      },
      {
        name: '生产商名称',
        code: 'name',
        width: 160,
        features: {
          sortable: true,
        },
      },
    ],
    // 特殊的自定义请求前参数，params为form表单数据
    customPrevPost: (customPrevPost: any) => {
      const { custom_attributeList } = customPrevPost;
      // 注意多选和单选 “sub_titles” 都要传 数组
      const custom_attribute =
        custom_attributeList?.filter((item: any) => {
          const key = `${item.id}${item.title}`;
          if (customPrevPost[key]) {
            item.sub_titles =
              item.component_type === 'SINGLE_CHOICE'
                ? [customPrevPost[key]]
                : customPrevPost[key]; // 过滤出已选择的数组
            // 只返回 sub_titles 数组长度大于 0 的项
            return item.sub_titles.length > 0;
          }
          return false;
        }) || [];

      const data = {
        ...customPrevPost,
        custom_attribute:
          custom_attribute?.length > 0 ? custom_attribute : null, //没有值后不需要传空数组，防止报错
        custom_attributeList: null,
      };

      return data;
    },
    formList: (settings: any) => {
      const { customattribute } = settings;
      // 处理自定义属性，注意只保存单选和多选
      const formCustomAttribute: any =
        customattribute?.map((item: any) => ({
          label: item.title,
          name: `${item.id}${item.title}`,
          type: 'select',
          multiple: item.component_type === 'MULTI_CHOICE',
          options:
            item.sub_titles?.map((v: any) => ({ value: v, label: v })) || [],
          onChange(e: any, form: FormInstance) {
            form.setFieldValue(`custom_attributeList`, customattribute); //每次选择，给form 赋值，
          },
        })) || [];

      return [
        {
          type: 'input',
          label: '关键字',
          name: 'keyword',
          suffix: true,
        },
        ...formCustomAttribute,
      ];
    },
  },
  producerandexecutivestandard: {
    title: '选择生产商',
    url: '/erp-mdm/hxl.erp.supplier.producerandexecutivestandard.find',
    columns: [
      {
        name: '序号',
        code: '_index',
        width: 60,
      },
      {
        name: '生产商名称',
        code: 'name',
        width: 160,
        features: {
          sortable: true,
        },
      },
      {
        name: '执行标准',
        code: 'executive_standard',
        width: 120,
        align: 'center',
        render: (value: any, record: any, index: any) => {
          return record._edit ? (
            <XlbInput
              key={record.executive_standard}
              size="small"
              className="full-box"
              defaultValue={value}
              onClick={(e) => e.stopPropagation()}
              onChange={(e: any) => {
                record.executive_standard = e.target.value;
              }}
            />
          ) : (
            <div className="info overwidth">{value}</div>
          );
        },
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
  },
};
