import { SUMMARY_TYPE_LIST } from '@/constants/purchaseOrdering';
import omit from 'lodash/omit';

export const PurchaseOrderingKeyMap = {
  /**@name 配送中心门店 inputDialog 单选 */
  erpCenterStoreIds: 'erpCenterStoreIds',
  erpPurchaseStorehouseId: 'erpPurchaseStorehouseId',
  erpStorehouseId: 'erpStorehouseId',
  erpIsSuggestOrder: 'erpIsSuggestOrder',
  erpInventory: 'erpInventory',
  erpOrderDate: 'erpOrderDate',
  erpOnOrderQuantity: 'erpOnOrderQuantity',
  erpReceiveDate: 'erpReceiveDate',
  erpSafeStockQuantity: 'erpSafeStockQuantity',
  erpDeliveryCycle: 'erpDeliveryCycle',
  erpPresentRule: 'erpPresentRule',
  erpItemPurchaseSpec: 'erpItemPurchaseSpec',
  erpStorehouseName: 'erpStorehouseName',
  erpItemSaleDateBegin: 'erpItemSaleDateBegin',
  erpItemSaleDateEnd: 'erpItemSaleDateEnd',
  erpSummary: 'erpSummary',
};

export const purchaseOrderingConfig: any[] = [
  // 智能订货
  {
    tag: 'ERP',
    label: '门店',
    id: PurchaseOrderingKeyMap?.erpCenterStoreIds,
    name: 'store_ids',
    fieldProps: {
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isMultiple: true,
        data: {
          center_flag: true,
        },
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      },
    },
    formItemProps: {
      label: '门店',
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '仓库',
    id: PurchaseOrderingKeyMap?.erpPurchaseStorehouseId,
    name: 'storehouse_id',
    dependencies: ['store_ids'],
    async request(params: any, anybaseURL: any, globalFetch: any) {
      const { store_ids: store_id } = params;
      console.log('store_id', store_id);
      if (!Array.isArray(store_id) || !store_id.length) {
        console.warn('store_id不存在');
        return [];
      }

      if (store_id.length > 1) {
        console.warn('多个store_id');
        return [];
      }
      const store_idT = store_id[0];
      const result = await globalFetch.post(
        `${anybaseURL}/erp/hxl.erp.storehouse.store.find`,
        {
          ...omit(params, 'store_ids'),
          store_id: store_idT,
        },
      );
      if (Array.isArray(result.data)) {
        return result.data.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
      }
    },
    disabled: (params: any) => params.store_ids?.length !== 1,
    // showSearch:true,
    handleDefaultValue(data: any) {
      const defaultStoreHouse =
        data.find((item: any) => item.default_flag) || data[0];
      return data?.length ? defaultStoreHouse?.value : null;
    },
    componentType: 'select',
  },
  {
    tag: 'ERP',
    label: '',
    id: PurchaseOrderingKeyMap?.erpIsSuggestOrder,
    name: 'advise_order',
    componentType: 'radio',
    formItemProps: {
      label: ' ',
      colon: false,
      initialValue: true,
    },
    fieldProps: {
      options: [
        { label: '全部', value: '' },
        { label: '建议订货', value: true },
        { label: '不建议订货', value: false },
      ],
    },
  },
  {
    tag: 'ERP',
    label: '',
    id: PurchaseOrderingKeyMap?.erpSummary,
    name: 'summary',
    componentType: 'checkbox',
    fieldProps: {
      options: SUMMARY_TYPE_LIST,
    },
    formItemProps: {
      label: ' ',
      colon: false,
    },
  },
  {
    tag: 'ERP',
    label: '库存',
    id: PurchaseOrderingKeyMap?.erpInventory,
    name: 'stock_quantity',
    componentType: 'input',
  },
  {
    tag: 'ERP',
    label: '订货日',
    id: PurchaseOrderingKeyMap.erpOrderDate,
    name: 'order_date',
    componentType: 'input',
  },
  {
    tag: 'ERP',
    label: '在订量',
    id: PurchaseOrderingKeyMap.erpOnOrderQuantity,
    name: 'on_order_quantity',
    componentType: 'input',
  },
  {
    tag: 'ERP',
    label: '到货日期',
    id: PurchaseOrderingKeyMap.erpReceiveDate,
    name: 'receive_date',
    componentType: 'input',
  },
  {
    tag: 'ERP',
    label: '安全库存',
    id: PurchaseOrderingKeyMap.erpSafeStockQuantity,
    name: 'safe_stock_quantity',
    componentType: 'input',
  },
  {
    tag: 'ERP',
    label: '交货周期',
    id: PurchaseOrderingKeyMap.erpDeliveryCycle,
    name: 'delivery_cycle',
    componentType: 'input',
  },
  {
    tag: 'ERP',
    label: '搭赠规则',
    id: PurchaseOrderingKeyMap.erpPresentRule,
    name: 'present_rule',
    componentType: 'input',
  },
  {
    tag: 'ERP',
    label: '采购规格',
    id: PurchaseOrderingKeyMap.erpItemPurchaseSpec,
    name: 'item_purchase_spec',
    componentType: 'input',
  },
  {
    tag: 'ERP',
    label: '补货仓库',
    id: PurchaseOrderingKeyMap?.erpStorehouseName,
    name: 'storehouse_name',
    componentType: 'input',
  },
  {
    tag: 'ERP',
    label: '预计销售开始时间',
    id: PurchaseOrderingKeyMap.erpItemSaleDateBegin,
    name: 'sale_date_begin',
    componentType: 'input',
  },
  {
    tag: 'ERP',
    label: '预计销售结束时间',
    id: PurchaseOrderingKeyMap.erpItemSaleDateEnd,
    name: 'sale_date_end',
    componentType: 'input',
  },
];
