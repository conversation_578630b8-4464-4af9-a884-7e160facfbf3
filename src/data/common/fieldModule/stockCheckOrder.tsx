import omit from 'lodash/omit'

export const stockCheckOrderKeyMap = {
  erpStockCheckOrderStore: 'erpStockCheckOrderStore',
  erpStockCheckOrderStoreStash: 'erpStockCheckOrderStoreStash',
  erpStockCheckOrderItemId: 'erpStockCheckOrderItemId'
}

export const stockCheckOrderConfig: any[] = [
  {
    tag: 'ERP',
    label: '门店',
    id: stockCheckOrderKeyMap?.erpStockCheckOrderStore,
    name: 'store_ids',
    fieldProps: (form) => {
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            status: true
          }
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name'
        }
      }
    },
    formItemProps: {
      label: '门店'
    },
    componentType: 'inputDialog'
  },
  {
    tag: 'ERP',
    componentType: 'select',
    label: '盘点仓库',
    name: 'storehouse_ids',
    dependencies: ['store_ids'],
    id: stockCheckOrderKeyMap?.erpStockCheckOrderStoreStash,
    request: async (params: any, anybaseURL: any, globalFetch: any) => {
      console.log("🚀 ~ request: ~ params:", params)
      const { store_ids } = params
      let store_idT: number
      if (!Array.isArray(store_ids) || !store_ids.length) {
        console.warn('store_ids不存在')
        return []
      }

      if (store_ids.length > 1) {
        console.warn('多个store_ids')
        return []
      }
      store_idT = store_ids[0]
      const result = await globalFetch.post(`${anybaseURL}/erp/hxl.erp.storehouse.store.find`, {
        ...omit(params, 'store_ids'),
        store_id: store_idT
      })
      if (Array.isArray(result.data)) {
        return result.data.map((item: any) => {
          return {
            label: item.name,
            value: item.id
          }
        })
      }
    },
    fieldProps: {
      mode: 'multiple',
      allowClear: true
    }
  },
  {
    tag: 'ERP',
    label: '商品档案',
    id: stockCheckOrderKeyMap.erpStockCheckOrderItemId,
    name: 'item_ids',
    fieldProps: {
      dialogParams: {
        type: 'goods',
        dataType: 'lists',
        isMultiple: true,
        data: {
          status: 1
        }
      }
    },
    componentType: 'inputDialog'
  }
]
