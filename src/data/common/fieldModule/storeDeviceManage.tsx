import type { CreateMapItem } from '@xlb/components/dist/lowcodes/XlbProForm/type'

export const StoreDeviceManageKeyMap = {
  // 设备类型
  erpDeviceType: 'erpDeviceType',
  // 设备状态
  erpDeviceStatus: 'erpDeviceStatus'
}

export const storeDeviceManageConfig: CreateMapItem[] = [
  {
    tag: 'ERP',
    id: 'erpDeviceType',
    componentType: 'select',
    label: '设备类型',
    name: '_category_ids',
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(anybaseURL + '/erp/hxl.erp.storehardware.category.find', {
        company_ids: formValues.company_ids || []
      })
      if (res.code == 0) {
        return res?.data?.map((item: any) => {
          return {
            label: item.name,
            value: item.id
          }
        })
      }
      return []
    },
  },
  {
    tag: 'ERP',
    label: '设备状态',
    id: StoreDeviceManageKeyMap.erpDeviceStatus,
    name: '_status',
    componentType: 'select',

    fieldProps: {
      options: [
        {
          label: '正常',
          value: 'ENABLE',
          type: 'success'
        },
        {
          label: '报修中',
          value: 'REPAIRING',
          type: 'warning'
        },
        {
          label: '停用',
          value: 'DISABLE',
          type: 'danger'
        }
      ]
    }
  }
]
