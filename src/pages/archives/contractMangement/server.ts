import XlbFetch from '@/utils/XlbFetch';
import { XlbFetch as fetch } from '@xlb/utils/';


export default class Api {
  //详情
  static readItem = async (data: any) => {
    return await XlbFetch('/erp/hxl.erp.contract.read', {
      ...data,
    });
  };
  static viewContract = async (data: any) => {
    return await XlbFetch('/erp/hxl.erp.contract.view', {
      ...data,
    });
  };
  static getUrl = async (data: any) => {
    return await XlbFetch('/erp/hxl.erp.contract.download', {
      ...data,
    });
  };
  static fentchContractEnum = async(data= {}) =>{
    return await XlbFetch('/erp/hxl.erp.contract.enum',data)
  }
  static fentchModuleName = async(data= {}) =>{
    return await XlbFetch('/erp/hxl.erp.contract.template.page',data)
  }
  static fentchModuleCreate = async(data= {}) =>{
    return await XlbFetch('/erp/hxl.erp.contract.create',data)
  }
  static fentchWhitelist = async(data= {}) =>{
    return await XlbFetch('/erp/hxl.erp.contract.whitelist.page',data)
  }
  static createWhite = async(data= {}) =>{
    return await XlbFetch('/erp/hxl.erp.contract.whitelist.create',{is_online:true,...data})
  }
 
    static exportWWW = (
    url: string,
    body: any,
    options?: { [key: string]: any },
  )=> {
    return XlbFetch<any>(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    });
  }



// 新品采购计划导出
static exportWhite = async (data: any) => {
  return await fetch.post(
    `${process.env.BASE_URL}` + '/erp/hxl.erp.contract.whitelist.export',
    data,
    {
      responseType: 'blob',
    },
  );
};
}

