import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbModal,
  XlbPageContainer,
} from '@xlb/components';
import { useEffect, useRef, useState } from 'react';
import Item from './item/index';
import API from './server';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;
const formList = [
  {
    label: '关键字',
    name: 'keyword',
    type: 'input',
    allowClear: true,
    check: true,
  },
];

const Index = () => {
  const modalRef = useRef(null);
  const itemRef = useRef(null);
  const [step, setStep] = useState(1);
  const [currentParams, setCurrentParams] = useState({});
  const [visible, setVisible] = useState(false);
  const [form] = XlbBasicForm.useForm();
  const pageModalRef = useRef(null);
  const closeModal = useRef(() => {});
  const getData = useRef(null);
  const closeDialog = () => {
    setVisible(false);
  };
  const columns = [
    {
      name: '序号',
      code: '_index',
      align: 'center',
      width: 50,
    },
    {
      name: '合同名称',
      code: 'name',
      width: 200,
      render: (t, record, item) => {
        return (
          <span
            onClick={() => {
              console.log(record, item);
              setCurrentParams({ ...record });
              // pageModalRef.current.open(); //
              setVisible(true);
            }}
            className="xlb-table-clickBtn"
          >
            {t}
          </span>
        );
      },
    },
    {
      name: '腾讯模板ID',
      code: 'channel_template_id',
      features: { copy: true },
      width: 360,
    },
  ];
  closeModal.current = () => {
    setVisible(false);
    return;
  };
  useEffect(() => {
    API.fentchContractEnum({ type: 'CONTRACT_TYPE' });

    console.log(modalRef);
  }, []);
  return (
    <>
      {/* <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          const fn = () => {
            onClose();
            setVisible(false);
            return;
          };
          closeModal.current = fn;

          return (
            <>
              <div style={{ padding: 10 }}>
                <XlbButton onClick={() => onClose()} type="primary">
                  返回
                </XlbButton>
              </div>
              <Item
                setStep={setStep}
                getData={getData.current}
                close={closeModal.current}
                params={currentParams}
              ></Item>
            </>
          );
        }}
      >
        <span></span>
      </XlbProPageModal> */}
      <XlbPageContainer
        url="/erp/hxl.erp.contract.template.page"
        immediatePost={true}
        tableColumn={columns}
        prevPost={(p) => {
          const keyword = form.getFieldValue('keyword');
          return { ...p, keyword };
        }}
      >
        <XlbModal
          width={800}
          open={visible}
          // isCancel
          okText={'保存'}
          isConfirm={step === 1}
          onCancel={closeDialog}
          onOk={() => itemRef.current?.save()}
          footerExtroContent={
            <>
              <XlbButton.Group>
                <XlbButton
                  onClick={() => {
                    itemRef.current?.onCancel();
                    setVisible(false);
                  }}
                  type="primary"
                >
                  取消
                </XlbButton>
                {step === 0 ? (
                  <XlbButton
                    onClick={() => itemRef.current?.next()}
                    style={{ marginRight: 6 }}
                  >
                    下一步
                  </XlbButton>
                ) : (
                  <XlbButton
                    style={{ marginRight: 6 }}
                    onClick={() => itemRef.current?.onCancel()}
                    type="primary"
                  >
                    上一步
                  </XlbButton>
                )}

                {/* <XlbButton>确定</XlbButton> */}
              </XlbButton.Group>
            </>
          }
          title="签署模板"
        >
          <Item
            ref={itemRef}
            setStep={setStep}
            getData={getData.current}
            close={closeModal.current}
            params={currentParams}
          ></Item>
        </XlbModal>
        <ToolBtn>
          {({ fetchData, selectRowKeys }) => {
            getData.current = fetchData;
            return (
              <>
                <XlbButton.Group>
                  <XlbButton
                    onClick={() => fetchData()}
                    type="primary"
                    icon={<XlbIcon size={16} name="sousuo" />}
                  >
                    查询
                  </XlbButton>
                  <XlbButton
                    onClick={() => {
                      setCurrentParams({});
                      // pageModalRef.current.open();
                      setVisible(true);
                    }}
                    icon={<XlbIcon size={16} name="jia" />}
                    type="primary"
                  >
                    新增
                  </XlbButton>
                  <XlbButton
                    disabled={selectRowKeys?.length === 0}
                    type="primary"
                    icon={<XlbIcon size={16} name="shanchu" />}
                    onClick={async () => {
                      await API.fentchDel({ id: selectRowKeys[0] });
                      fetchData();
                    }}
                  >
                    删除
                  </XlbButton>
                </XlbButton.Group>
              </>
            );
          }}
        </ToolBtn>
        <SearchForm>
          <XlbForm isHideDate formList={formList} form={form}></XlbForm>
        </SearchForm>
        <Table selectMode="single" primaryKey="id" key="id" />
      </XlbPageContainer>
    </>
  );
};
export default Index;
