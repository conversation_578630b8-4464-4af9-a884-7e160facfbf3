import { message } from 'antd'
import { XlbTipsModal, XlbBasicForm, XlbInput, XlbProPageContainer } from '@xlb/components'
import { useEffect, useRef, useState } from 'react'
import Api from './server'
import { hasAuth } from '@/utils'

export enum DataType {
  LISTS = 'lists',
  TREE = 'tree'
}

const DevicesBrandIndex = () => {
  const [treeForm] = XlbBasicForm.useForm()
  const [treeDataSource, setTreeDataSource] = useState<any[]>([])
  const [selectedCategory, setSelectedCategory] = useState()

  const onMenuClick = async (key: any, data: any) => {
    if (key === 'delete') {
      const res = await Api.getStorehardwareFind({ category_id: data.id })
      if (res.code === 0 && res?.data?.length > 0) {
        return message.error('当前分类下存在设备，不可删除')
      }
      await XlbTipsModal({
        tips: `是否要删除分类${data.name}`,
        isCancel: true,
        onOkBeforeFunction: async () => {
          const res = await Api.getStorageDelete({ id: data.id })
          if (res.code === 0) {
            message.success('操作成功')
            getDeviceTypes()
            return true
          }
        }
      })
    } else {
      treeForm.setFieldsValue({
        name: key === 'edit' ? data.name : ''
      })
      XlbTipsModal({
        tips: (
          <XlbBasicForm autoComplete="off" colon={true} form={treeForm} labelCol={{ span: 6 }}>
            <XlbBasicForm.Item
              label="分类名称"
              name="name"
              rules={[{ required: true, message: '请输入分类名称' }]}
            >
              <XlbInput
                style={{ width: 260 }}
                size="small"
                placeholder="请输入分类名称"
                maxLength={30}
              />
            </XlbBasicForm.Item>
          </XlbBasicForm>
        ),
        destroyOnClose: true,
        isCancel: true,
        onCancel: () => treeForm.resetFields(),
        onOkBeforeFunction: async () => {
          try {
            await treeForm.validateFields()
          } catch (err: any) {
            return false
          }
          const params = { ...treeForm.getFieldsValue() }
          const res =
            key === 'add'
              ? await Api.getStorageSave({ ...params, parent_id: 0 })
              : await Api.getStorageUpdate({ ...params, id: data.id })
          if (res.code === 0) {
            message.success('操作成功')
            treeForm.resetFields()
            getDeviceTypes()
            return true
          }
        }
      })
    }
  }

  const getDeviceTypes = async () => {
    const res = await Api.getTypes({})
    if (res.code === 0) {
      setTreeDataSource(res?.data?.filter((v) => v?.id))
    }
  }

  useEffect(() => {
    getDeviceTypes()
  }, [])

  return (
    <XlbProPageContainer
      searchFieldProps={{ formList: ['keyword'] }}
      details={{
        isCancel: true,
        hiddenSaveBtn: true,
        primaryKey: 'id',
        title: (obj) => {
          return <div>{obj?.id ? '编辑' : '新增'}</div>
        },
        mode: 'modal',
        initialValues: {
          category_id: selectedCategory ? selectedCategory : null
        },
        formList: [
          {
            componentType: 'form',
            fieldProps: {
              formList: [
                {
                  label: '品牌名称',
                  id: 'name',
                  rules: [{ required: true, message: '请输入硬件名称' }],
                  itemSpan: 20
                },
                {
                  label: '关联类型',
                  id: 'device_id',
                  rules: [{ required: true, message: '请选择设备类型' }],
                  fieldProps: {
                    allowClear: true
                  },
                  itemSpan: 20
                }
              ]
            }
          }
        ],
        updateFieldProps: {
          url: '/erp/hxl.erp.storehardware.brand.update',
          beforePost: (formValues) => {
            return {
              ...formValues,
              categories: [formValues?.category_id]
            }
          }
        }
      }}
      treeFieldProps={{
        dataSource: treeDataSource,
        leftKey: 'category_id',
        dataType: DataType.LISTS,
        onSelect: (e) => {
          console.log('333:', e)
          setSelectedCategory(e?.id)
        },

        menu: (data) => {
          return [
            {
              name: '新增分类',
              key: 'add'
            },
            {
              name: '编辑分类',
              key: 'edit'
            },
            {
              name: '删除分类',
              key: 'delete'
            }
          ]
        },
        onMenuClick: onMenuClick
      }}
      addFieldProps={{
        name: '新增',
        url: hasAuth(['设备品牌管理', '编辑'])
          ? '/erp/hxl.erp.storehardware.brand.save'
          : undefined,
        beforePost: (formValues) => {
          return {
            ...formValues,
            categories: [formValues?.category_id]
          }
        }
      }}
      deleteFieldProps={{
        url: hasAuth(['设备品牌管理', '删除'])
          ? '/erp/hxl.erp.storehardware.brand.delete'
          : undefined
      }}
      tableFieldProps={{
        url: '/erp/hxl.erp.storehardware.brand.find',
        selectMode: 'multiple',
        showColumnsSetting: false,
        immediatePost: true,
        tableColumn: [
          {
            name: '序号',
            code: '_index',
            width: 60,
            align: 'center'
          },
          {
            name: '品牌名称',
            code: 'name',
            width: 160,
            features: { sortable: true, details: true },
            align: 'left'
          },
          {
            name: '关联类型',
            code: 'category_id',
            width: 160,
            features: { sortable: true },
            align: 'left',
            render: (text, record, index) => {
              return <div>{text ? record?.category_name : ''}</div>
            }
          }
        ]
      }}
    />
  )
}

export default DevicesBrandIndex
