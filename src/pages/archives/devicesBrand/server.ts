import { XlbFetch as ErpRequest } from '@xlb/utils';

export default {
  deleteItem: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.storehardware.brand.delete', { data }),
  //查询
  getStorehardwareFind: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storehardware.brand.find', {
      data,
    });
  },

  getTypes: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.storehardware.category.find', data),
  saveItem: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.storehardware.brand.save', { data }),
  updateItem: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.storehardware.brand.update', { data }),
  //删除
  getStorageDelete: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storehardware.category.delete', {
      data,
    });
  },
  //查询
  getStorageFind: async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.storehardware.category.find',
      data,
    );
  },
  //编辑
  getStorageUpdate: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storehardware.category.update', {
      data,
    });
  },
  //新增
  getStorageSave: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storehardware.category.save', {
      data,
    });
  },
};
