import { useState, Fragment } from 'react'
import { Modal, Input, Radio, Space, message, Checkbox, Select, Form, Button } from 'antd'
import style from './batchChange.less'
import { batch } from '../data'
const { Option } = Select
import Api from '../server'
import {
  XlbInputDialog,
  XlbInputNumber,
  XlbTipsModal,
  XlbButton,
  XlbImportModal,
  XlbModal
} from '@xlb/components'
import { XlbProgress } from '@/components/common'
import NiceModal from '@ebay/nice-modal-react'
const BatchChange = (props: any) => {
  const { tabKey, enableOrganization } = props
  const { visible, hide, resolve } = NiceModal.useModal()
  const [loading, setloading] = useState(false)
  const [storeNamesData, setStoreNames] = useState<any>()
  const [form] = Form.useForm()
  const handleCancel = (flag: boolean) => {
    resolve(flag)
    hide()
  }

  // 导入门店
  const importStores = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.storename.import`,
      templateUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.storecodetemplate.download`,
      templateName: '门店导入模板',
      params: {
        checkOrg: enableOrganization && tabKey !== 'order_num' ? true : false
      },
      callback: (res: any) => {
        if (res.code !== 0) return
        form.setFieldsValue({
          org_id: enableOrganization ? res?.data?.org_ids?.join(',') : undefined,
          org_id_name: enableOrganization ? res?.data?.org_names?.join(',') : undefined,
          store_ids: res?.data?.store_ids || [],
          store_ids_name: res?.data?.store_names || []
        })
      }
    })
  }
  // 导入商品
  const importShort = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.items.batchimport`,
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.item.shorttemplate.download`,
      templateName: '商品导入模板',
      callback: (res: any) => {
        if (res.code !== 0) return
        form.setFieldsValue({
          item_ids: res?.data?.items?.map((v: any) => v.id),
          item_ids_name: res?.data?.items?.map((v: any) => v.name)
        })
      }
    })
  }

  const handleOk = async () => {
    if (form.getFieldValue('store_ids') === undefined) {
      XlbTipsModal({
        tips: '请选择门店'
      })
      return
    }
    const { checkValue, multiple, upper_limit, lower_limit } = form.getFieldsValue(true)
    if (
      form.getFieldValue('radioValue') !== 3 &&
      form.getFieldValue('radioValue') !== 2 &&
      form.getFieldValue('radioValue') !== 1 &&
      form.getFieldValue('radioValue') !== 4
    ) {
      XlbTipsModal({
        tips: '请先选择修改范围'
      })
      return
    }

    if (form.getFieldValue('radioValue') == 2 && !form.getFieldValue('item_category_ids')?.length) {
      XlbTipsModal({
        tips: '请选择商品类别'
      })
      return
    }
    if (form.getFieldValue('radioValue') == 3 && !form.getFieldValue('item_ids')?.length) {
      XlbTipsModal({
        tips: '请选择商品档案'
      })
      return
    }
    if (form.getFieldValue('checkValue') == undefined || form.getFieldValue('checkValue') == '') {
      XlbTipsModal({
        tips: '请先选择修改数据'
      })
      return
    }
    const data = {
      ...form.getFieldsValue(true),
      org_id: Array.isArray(form.getFieldValue('org_id'))
        ? form.getFieldValue('org_id')[0]
        : form.getFieldValue('org_id'),
      item_category_ids: form.getFieldValue('item_category_ids'),
      item_ids: form.getFieldValue('item_ids'),
      store_ids: form.getFieldValue('store_ids'),
      types: tabKey === 'order_num' ? ['WHOLESALE'] : '[REQUEST]'
    }
    batch.forEach((item: any) => {
      // 筛选勾选项并赋值
      if (form.getFieldValue('checkValue')?.includes(item.value)) {
        data[item.value] =
          form.getFieldValue(item.value) === undefined ? false : form.getFieldValue(item.value)
      } else {
        data[item.value] = undefined
      }
    })
    if (form.getFieldValue('checkValue').includes('lower_limit')) {
      if (
        Number(form.getFieldValue('lower_limit')) < 0 ||
        Number(form.getFieldValue('lower_limit')) > 999999999
      ) {
        XlbTipsModal({
          tips: '每单订购下限需输入0-999999999区间的数值'
        })
        return
      }
    }
    if (form.getFieldValue('checkValue').includes('multiple')) {
      if (
        Number(form.getFieldValue('multiple')) < 0 ||
        Number(form.getFieldValue('multiple')) > 999999999
      ) {
        XlbTipsModal({
          tips: '订购倍数需输入0-999999999区间的数值'
        })
        return
      }
    }
    if (form.getFieldValue('checkValue').includes('upper_limit')) {
      if (
        Number(form.getFieldValue('upper_limit')) < 0 ||
        Number(form.getFieldValue('upper_limit')) > 999999999
      ) {
        XlbTipsModal({
          tips: '配送日订购上限需输入0-999999999区间的数值'
        })
        return
      }
    }
    setloading(true)
    data.multiple =
      checkValue.includes('multiple') && !multiple
        ? -1
        : checkValue.includes('multiple')
        ? multiple
        : null
    data.upper_limit =
      checkValue.includes('upper_limit') && !upper_limit
        ? -1
        : checkValue.includes('upper_limit')
        ? upper_limit
        : null
    data.lower_limit =
      checkValue.includes('lower_limit') && !lower_limit
        ? -1
        : checkValue.includes('lower_limit')
        ? lower_limit
        : null
    setloading(false)

    if (tabKey == 'store_order_num') {
      const { store_ids, store_names } = form.getFieldsValue(true)
      const storeNames = store_names?.split(',') || []

      const storeCondition = store_ids.map((id: number, index: number) => {
        return {
          ...data,
          store_ids: [id]
        }
      })
      if (checkValue.includes('multiple') && storeNamesData?.length > 0) {
        XlbTipsModal({
          tips: `普通门店不允许设置订购倍数【${storeNamesData}】不符合！`
        })
        return
      }
      NiceModal.show(XlbProgress, {
        requestApi: '/erp/hxl.erp.storeorderattribute.batchupdate',
        items: storeCondition || [],
        titleList: storeNames,
        promptTitle: '正在操作：'
      }).then((res: any) => {
        if (res.code == 0) {
          message.success('更新成功')
          handleCancel(true)
          form.getFieldValue('radioValue') == ''
          form.resetFields()
        }
      })
    } else {
      const res = await Api[tabKey === 'order_num' ? 'batchUpdate' : 'storeBatchUpdate'](data)
      if (res.code === 0) {
        message.success('更新成功')
        handleCancel(true)
        form.getFieldValue('radioValue') == ''
        form.resetFields()
      }
    }
  }

  const renderCheckBox = (item: string) => {
    switch (item) {
      case '每单订购下限':
      case '配送日订购上限':
      case '备货订货上限':
        return <Input style={{ width: 140 }} size="small" />
      case '订购倍数':
        return (
          <XlbInputNumber min={0} max={9999} precision={0} style={{ width: 140 }} size="small" />
        )
      default:
        return (
          <Select defaultValue={false} size="small">
            <Select value={true}>是</Select>
            <Option value={false}>否</Option>
          </Select>
        )
    }
    // })
  }

  return (
    <XlbModal
      title={'订购数量批量修改'}
      centered
      open={visible}
      maskClosable={false}
      onOk={handleOk}
      onCancel={() => {
        form.resetFields()
        handleCancel(false)
      }}
      width={490}
      confirmLoading={loading}
    >
      <Form form={form}>
        <div className={style.box}>
          <p className={style.title}>修改门店</p>
          {enableOrganization && tabKey !== 'order_num' ? (
            <Form.Item
              label="组织"
              name="org_id"
              style={{ display: 'inline-block', marginLeft: '38px' }}
            >
              <XlbInputDialog
                style={{ width: '180px' }}
                treeModalConfig={{
                  title: '选择组织',
                  url: '/erp-mdm/hxl.erp.org.find',
                  dataType: 'lists',
                  checkable: false, // 是否多选
                  primaryKey: 'id',
                  afterPost: (data: any) => {
                    return data.filter((item: any) => item.level === 2)
                  },
                  params: {
                    level: 2
                  }
                }}
              />
            </Form.Item>
          ) : null}
          <Form.Item noStyle dependencies={['org_id']}>
            {({ getFieldValue }) => (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Form.Item name="store_ids" label="选择门店">
                  <XlbInputDialog
                    style={{ width: '148px' }}
                    dialogParams={{
                      type: 'store',
                      isMultiple: true,
                      nullable: true,
                      data: getFieldValue('org_id')?.length
                        ? {
                            org_ids: getFieldValue('org_id'),
                            status: true
                          }
                        : { status: true },
                      onOkBeforeFunction: (_: any, data: any[]) => {
                        form.setFieldsValue({
                          store_names: data.map((item) => item.store_name).join(',')
                        })
                        const storeNames = data
                          .filter((store) => store.general_store_flag)
                          .map((store) => store.store_name)
                        setStoreNames(storeNames)
                        return true
                      }
                    }}
                    fieldNames={{
                      idKey: 'id',
                      nameKey: 'store_name'
                    }}
                    width={260}
                  />
                </Form.Item>
                <XlbButton size="small" onClick={() => importStores()}>
                  导入
                </XlbButton>
              </div>
            )}
          </Form.Item>
        </div>
        <div className={style.box}>
          <p className={style.title}>选择范围</p>
          <Form.Item name="radioValue">
            <Radio.Group>
              <Space direction="vertical">
                <Radio value={1}>全部商品</Radio>
                <Radio value={2}>
                  商品类别
                  <Form.Item name="item_category_ids">
                    <XlbInputDialog
                      style={{ width: '180px' }}
                      treeModalConfig={{
                        title: '选择商品分类', // 标题
                        url: '/erp-mdm/hxl.erp.category.find', // 请求地址
                        dataType: 'lists',
                        checkable: true, // 是否多选
                        primaryKey: 'id',
                        width: 360 // 模态框宽度
                      }}
                    />
                  </Form.Item>
                </Radio>
                <Radio value={3}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    商品档案
                    <Form.Item name="item_ids">
                      <XlbInputDialog
                        dialogParams={{
                          type: 'goods',
                          isMultiple: true,
                          nullable: true,
                          data: {
                            filter_item_types: ['COMBINATION', 'MAKEBILL']
                          },
                          onOkBeforeFunction: (_: any, data: any[]) => {
                            if (data && data?.length > 3000) {
                              XlbTipsModal({
                                tips: '不允许超过3000条商品',
                                zIndex: 9999
                              })
                              return false
                            }
                            return true
                          }
                        }}
                        width={180}
                      />
                    </Form.Item>
                    <XlbButton size="small" onClick={() => importShort()}>
                      导入
                    </XlbButton>
                  </div>
                </Radio>
              </Space>
            </Radio.Group>
          </Form.Item>
        </div>
        <div className={style.box} style={{ marginBottom: 10 }}>
          <p className={style.title}>修改数据</p>
          <Form.Item name="checkValue">
            <Checkbox.Group style={{ width: '100%' }}>
              {batch.map((item) => {
                if (item.label === '备货订货上限' && tabKey === 'order_num') return
                return (
                  <Fragment key={item.value}>
                    <Checkbox value={item.value} style={{ marginTop: 10 }}>
                      {item.label}
                    </Checkbox>
                    <Form.Item
                      name={item.value}
                      key={item.value}
                      style={{ marginTop: 8 }}
                      initialValue={
                        item.label === '每单订购下限' ||
                        item.label === '配送日订购上限' ||
                        item.label === '备货订货上限'
                          ? '0.000'
                          : item.label === '订购倍数'
                          ? '1'
                          : ''
                      }
                    >
                      {renderCheckBox(item.label)}
                    </Form.Item>
                  </Fragment>
                )
              })}
            </Checkbox.Group>
          </Form.Item>
        </div>
      </Form>
    </XlbModal>
  )
}
export default NiceModal.create(BatchChange)
