import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { hasAuth } from '@/utils';
import { XlbProPageContainer, XlbTipsModal } from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { FC, useEffect, useRef, useState } from 'react';
import Api from './server';
export const typeOptions = [
  { label: '业务组织', value: 'BUSINESS' },
  { label: '核算组织', value: 'ACCOUNTING' },
];
export enum DataType {
  LISTS = 'lists',
  TREE = 'tree',
}
const index: FC = () => {
  const [treeData, setTreeData] = useState<any[]>([]);
  const [current_level_length, set_current_level_length] = useState(0);
  const addUrl = useRef(null);
  const editData = useRef(null);
  const containerRef = useRef(null);
  const has_deafult_org = useRef(false); // 同级已经存在默认组织
  const getTree = async () => {
    const res = await ErpRequest.post('/erp-mdm/hxl.erp.org.tree', {});
    if (res.code === 0) {
      setTreeData(res.data);
    }
  };

  useEffect(() => {
    getTree();
  }, []);

  return (
    <XlbProPageContainer
      ref={containerRef}
      searchFieldProps={{
        formList: [{ id: 'commonInput', label: '关键字', name: 'keyword' }],
      }}
      treeFieldProps={{
        // leftUrl: '/erp-mdm/hxl.erp.org.tree',
        dataSource: treeData,
        dataType: DataType.LISTS,
        leftKey: 'parent_id',
      }}
      tableFieldProps={{
        url: '/erp-mdm/hxl.erp.org.page',
        tableColumn: [
          { name: '序号', code: '_index', width: 70, align: 'center' },
          {
            name: '组织代码',
            code: 'code',
            width: 120,
            features: { sortable: true },
          },
          {
            name: '组织名称',
            code: 'name',
            width: 120,
            features: { sortable: true, details: true },
          },
          {
            name: '组织属性',
            code: 'type',
            width: 120,
            features: { sortable: true },
            render: (text) =>
              typeOptions.find((item) => item.value === text)?.label,
          },
          {
            name: '关联门店',
            code: 'store_id',
            width: 120,
            features: { sortable: true },
            render: (_, record) => record.store_name,
          },
          {
            name: '创建时间',
            code: 'create_time',
            width: 212,
            features: { sortable: true, format: 'TIME' },
          },
          {
            name: '创建人',
            code: 'create_by',
            width: 120,
            features: { sortable: true },
          },
          {
            name: '更新时间',
            code: 'update_time',
            width: 212,
            features: { sortable: true, format: 'TIME' },
          },
          {
            name: '更新人',
            code: 'update_by',
            width: 120,
            features: { sortable: true },
          },
        ],
        selectMode: 'single',
        showColumnsSetting: false,
        immediatePost: true,
      }}
      details={{
        mode: 'modal',
        saveFieldProps: {
          hidden: () => true,
        },
        isCancel: true,
        title: (formValues) => `${formValues?.id ? '编辑' : '新增'}组织`,
        primaryKey: 'id',
        queryFieldProps: {
          url: '/erp-mdm/hxl.erp.org.read',
          afterPost: (data: any) => {
            console.log(data, 'DDDDDDD');
            editData.current = data;
            return { ...data, kingdeedisable: !!data?.kingdee_code };
          },
        },
        formList: [
          {
            componentType: 'form',

            fieldProps: {
              width: '100%',
              formList: [
                {
                  id: ErpFieldKeyMap?.erpOrgId,
                  label: '上级组织',
                  name: 'parent_id',
                  itemSpan: 24,
                  onChange(e, form, options) {
                    form?.setFieldValue('default_org', false);
                    if (options[0]?.level) {
                      form?.setFieldValue('level', options[0]?.level + 1);
                    }
                    if (options[0]?.level * 1 === 2) {
                      ErpRequest.post('/erp-mdm/hxl.erp.org.tree', {}).then(
                        (res: any) => {
                          console.log(res, options);
                          let current_level_len = 0; // 判断是否只有一个3级
                          res?.data.forEach((_: { parent_id: any }) => {
                            if (_.parent_id === options[0]?.id) {
                              ++current_level_len;
                            }
                          });
                          set_current_level_length(current_level_len);
                          if (current_level_len === 1) {
                            // form?.setFieldValue('enabled', true);
                            form?.setFieldValue('default_org', true);

                            if (options[0] && options[0]?.children?.length) {
                              // 如果已经有 default_org  set false
                              const hasDefaultOrg = options[0]?.children.some(
                                (child: { default_org: boolean }) => {
                                  return child.default_org === true;
                                },
                              );
                              form?.setFieldValue(
                                'default_org',
                                !hasDefaultOrg,
                              );
                            }
                            if (
                              options[0] &&
                              options[0]?.children?.length === 0
                            ) {
                              form?.setFieldValue('default_org', true);
                            }
                          }
                        },
                      );
                    }
                  },
                  dependencies: ['level', 'id'],
                  rules: [
                    ({ getFieldValue }) => ({
                      required: getFieldValue('level') !== 1,
                      validator: (_, value) => {
                        if (getFieldValue('level') === 1) {
                          return Promise.resolve();
                        }
                        if (value) {
                          return Promise.resolve();
                        }
                        return Promise.reject('上级组织不允许为空');
                      },
                    }),
                  ],
                  disabled: (formValues) =>
                    formValues?.level === 1 || !!formValues?.id,
                },
                {
                  id: 'commonInput',
                  label: '组织名称',
                  name: 'name',
                  itemSpan: 24,
                  rules: [{ required: true, message: '组织名称不允许为空' }],
                  fieldProps: { maxLength: 30 },
                },
                {
                  id: 'commonInput',
                  label: '组织代码',
                  name: 'code',
                  itemSpan: 24,

                  fieldProps: { maxLength: 10 },
                  rules: [
                    { required: true, message: '组织代码不允许为空' },
                    {
                      pattern: /^[a-zA-Z0-9]+$/,
                      message: '组织代码只能输入字母和数字',
                    },
                  ],
                },
                {
                  id: 'commonSelect',
                  label: '组织属性',
                  name: 'type',
                  // fieldProps: { mode: 'multiple' },
                  itemSpan: 24,
                  rules: [{ required: true, message: '组织属性不允许为空' }],
                  options: typeOptions,
                },
                {
                  id: 'commonInput',
                  label: '关联门店',
                  name: 'store_name',
                  itemSpan: 24,
                  rules: [{ required: true, message: '关联门店不允许为空' }],
                  dependencies: ['id'],
                  disabled(formValues) {
                    return !!formValues?.id;
                  },
                },
                {
                  id: ErpFieldKeyMap?.erpSupplierId,
                  label: '关联供应商',
                  name: 'supplier_id',
                  itemSpan: 24,
                },
                {
                  id: ErpFieldKeyMap?.erpWholesalerId,
                  label: '关联批发客户',
                  name: 'client_id',
                  itemSpan: 24,
                },
                {
                  id: ErpFieldKeyMap?.erpKingdeeCode,
                  name: 'kingdee_code',
                  label: '金蝶编码',
                  itemSpan: 24,
                  dependencies: ['kingdeedisable'],
                  disabled: (formValues) => formValues?.kingdeedisable,
                  rules: [
                    { required: true, message: '金蝶编码不允许为空' },
                    { max: 20, message: '金蝶编码长度不能超过20位' },
                  ],
                },
                {
                  dependencies: ['level'],
                  hidden: (formValues) => {
                    return (
                      !hasAuth(['组织管理', '编辑']) || formValues?.level !== 3
                    );
                  },
                  id: ErpFieldKeyMap?.erpDefaultOrg,
                  label: '               ', // label 为空
                  disabled: (formValues) => {
                    return (
                      editData.current?.id && editData.current?.default_org
                    ); // current_level_length === 1,
                  },
                  itemSpan: 24,
                  onChange: async (e, f) => {
                    console.log(containerRef);
                    has_deafult_org.current = false;
                    if (e) {
                      document
                        .querySelector('.ant-modal-footer  .xlb_btn_primary')
                        ?.setAttribute('disabled', 'true');
                      const res = await Api.findOrg({
                        parent_id: f?.getFieldValue('parent_id'),
                      });
                      document
                        .querySelector('.ant-modal-footer  .xlb_btn_primary')
                        ?.removeAttribute('disabled');
                      if (res?.data && res?.data?.length) {
                        has_deafult_org.current = res?.data?.some(
                          (_) => _.default_org,
                        );
                      } else {
                        has_deafult_org.current = true;
                      }

                      const data = f.getFieldsValue(true);
                      if (
                        res?.data?.length &&
                        data.default_org &&
                        e &&
                        !data.id &&
                        has_deafult_org.current
                      ) {
                        const result = await Api.checkInfo({ ...data });
                        if (result?.code === 2004) {
                          f?.setFieldValue('default_org', false);
                        }
                        const skip_check_phase_list = [];
                        if (
                          result?.code === 0 &&
                          result.data.show_prompt_flag
                        ) {
                          skip_check_phase_list.push(
                            result.data.prompt_msg_type,
                          );

                          XlbTipsModal({
                            tips: result.data.prompt_msg,
                            onCancel: () => {
                              f?.setFieldValue('default_org', false);
                            },
                            onOk: async () => {
                              // alert(1);
                              const result2 = await Api.checkInfo({
                                ...data,
                                skip_check_phase_list: skip_check_phase_list,
                              });
                              if (
                                result2.code === 0 &&
                                result2.data.show_prompt_flag
                              ) {
                                skip_check_phase_list.push(
                                  result.data.prompt_msg_type,
                                );
                                XlbTipsModal({
                                  tips: result2.data.prompt_msg,
                                  onOk: () => {
                                    // Api.update(data).then(() => {
                                    //   getTree();
                                    // });
                                  },
                                  onCancel: () => {
                                    f?.setFieldValue('default_org', false);
                                  },
                                });
                                // } else {
                                //   Api.save(data).then(() => {
                                //     getTree();
                                //   });
                              }
                            },
                          });
                          // } else {
                          //   Api.save(data).then(() => {
                          //     getTree();
                          //   });
                        }
                        // } else {
                        //   Api.save(data).then(() => {
                        //     getTree();
                        //   });
                      }
                    }
                    // 新增限制
                  },
                },
              ],
            },
          },
        ],
        updateFieldProps: {
          // url: '/erp/hxl.erp.server.org.check.default',
          beforePost: async (data) => {
            if (!has_deafult_org.current) {
              Api.update(data).then(() => {
                getTree();
              });
              return { ...data };
            }
            if (data.default_org && has_deafult_org.current) {
              const result = await Api.checkInfo({ ...data });
              const skip_check_phase_list = [];
              if (result.code === 0 && result.data.show_prompt_flag) {
                skip_check_phase_list.push(result.data.prompt_msg_type);
                XlbTipsModal({
                  tips: result.data.prompt_msg,
                  onOk: async () => {
                    // alert(1);
                    const result2 = await Api.checkInfo({
                      ...data,
                      skip_check_phase_list: skip_check_phase_list,
                    });
                    if (result2.code === 0 && result2.data.show_prompt_flag) {
                      skip_check_phase_list.push(result.data.prompt_msg_type);
                      XlbTipsModal({
                        tips: result2.data.prompt_msg,
                        onOk: () => {
                          Api.update(data).then(() => {
                            getTree();
                          });
                        },
                      });
                    } else {
                      Api.update(data).then(() => {
                        getTree();
                      });
                    }
                  },
                });
              } else {
                Api.update(data).then(() => {
                  getTree();
                });
              }
            } else {
              Api.update(data).then(() => {
                getTree();
              });
            }
            return { ...data };
          },
          afterPost: (data) => {
            console.log(data, 'CCCCCCCCCCCCCCCX');
            getTree();
          },
          // getTree(),
        },
      }}
      addFieldProps={{
        url: hasAuth(['组织管理', '编辑'])
          ? '/erp-mdm/hxl.erp.org.save'
          : undefined,
        beforePost: (data) => {
          return { ...data };
        },
        afterPost: () => getTree(),
      }}
      deleteFieldProps={{
        url: hasAuth(['组织管理', '删除'])
          ? '/erp-mdm/hxl.erp.org.delete'
          : undefined,
        disabled: true,
        afterPost: () => getTree(),
      }}
    />
  );
};

export default index;
