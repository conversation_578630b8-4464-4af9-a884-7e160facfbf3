import { orderStatusIcons } from '@/components/data';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import {
  XlbBaseUpload,
  XlbBasicData,
  XlbBasicForm,
  XlbButton,
  XlbDropdownButton,
  XlbIcon,
  XlbInput,
  XlbInputDialog,
  XlbInputNumber,
  XlbMessage,
  XlbPrintModal,
  XlbSelect,
  XlbShortTable,
  XlbTableColumnProps,
  XlbTabs,
  XlbTipsModal,
} from '@xlb/components';
import openTreeModal from '@xlb/components/dist/components/XlbTree/index.modal';
import { LStorage, XlbFetch } from '@xlb/utils';
import { cloneDeep } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { itemTableListDetail } from './data';
import style from './index.less';
import {
  approve,
  audit,
  check,
  detailexport,
  handle,
  print,
  read,
  save,
  unaudit,
  update,
} from './server';

const Item = (props: any) => {
  const [info, setInfo] = useState({ state: 'INIT' });
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [fid, setFid] = useState<any>(1);
  const [cargo_org_id, setCargo_org_id] = useState<any>(null);
  const [rowData, setRowData] = useState<any>([]);
  const [fileList, setFileList] = useState<any[]>([]);
  const [form] = XlbBasicForm.useForm();
  const { record, onBack } = props;
  const [storeHouse, setStoreHouse] = useState<any[]>([]);
  // 领用原因option
  const [collectReason, setCollectReason] = useState<any[]>([]);
  const { enable_organization, enable_cargo_owner } = useBaseParams(
    (state) => state,
  );
  const [itemArrdetail, setItemArrdetail] = useState<
    XlbTableColumnProps<any>[]
  >(cloneDeep(itemTableListDetail));
  const [disabledCargoOwner, setDisabledCargoOwner] = useState(false);

  const [baseInfoActiveKey, setBaseInfoActiveKey] = useState('baseInfo');
  const [edit, setEdit] = useState(false);
  const [isHasItemCode, setIsHasItemCode] = useState(false);
  const [footerData, setFooterData] = useState<any[]>([]);
  const [pagin, setPagin] = useState({ pageSize: 200, pageNum: 1, total: 0 });
  const [deptinfoId, setDeptinfoId] = useState<any>('');
  const selectedNodesRef = useRef<any>(null);

  // 获取仓库
  const getStoreHouse = async (id: any, isEdit: boolean) => {
    if (!id) {
      return;
    }
    const data = {
      store_id: id,
    };
    const res = await XlbFetch.post('/erp-mdm/hxl.erp.storehouse.store.find', data);
    if (res?.code == 0) {
      const options = res?.data
        .filter((v: any) => v.distribution)
        .map((item: any) => ({
          label: item.name,
          value: item.id,
          default_flag: item.default_flag,
        }));
      setStoreHouse(options);
      if (isEdit) {
        form.setFieldsValue({
          storehouse_id:
            options.find((v: any) => v.default_flag)?.value ||
            options[0]?.value,
        });
      }
    }
  };
  // 获取货主
  const getEnableCargoOwner = async (id: any) => {
    const data = {
      store_ids: [id],
      owner_type: 'ORGANIZATION',
    };
    const res = await XlbFetch.post(
      '/erp-mdm/hxl.erp.cargo.owner.pageforinner',
      data,
    );
    // 只有一个货主
    if (res?.data?.content?.length == 1) {
      form.setFieldsValue({
        cargo_owner_id: res.data.content[0].id,
        cargo_owner_name: res.data.content[0].name,
      });
      setDisabledCargoOwner(true);
    }
    if (res?.data?.content?.length !== 1) {
      form.setFieldsValue({
        cargo_owner_id: null,
        cargo_owner_name: null,
      });
      setDisabledCargoOwner(false);
    }
  };
  // 获取二级组织与货主
  const getSecondOrgAndCargoOwner = async (
    name: any,
    isEdit: boolean = true,
  ) => {
    //分割name
    const nameArr = name.split('/')?.[1];

    const res = await XlbFetch.post('/erp/hxl.erp.requisitionorder.deptorgid', {
      name: nameArr,
    });
    if (res?.code == 0 && res?.data) {
      if (isEdit) {
        form.setFieldsValue({
          cargo_owner_name: res?.data?.cargo_owner_name,
          cargo_owner_id: res?.data?.cargo_owner_id,
        });
      }
      setCargo_org_id(res?.data?.org_id);
    }
  };
  // 领用原因
  const getCollectReason = async () => {
    const res = await XlbFetch.post('/erp/hxl.erp.requisitionreason.find', {
      flag: undefined,
    });
    if (res?.code == 0) {
      const options = res?.data?.map((item: any) => ({
        label: item.name,
        value: item.id,
        disabled: !item.flag,
      }));
      setCollectReason(options);
    }
  };
  const inputChange = (e: any, code: string, record: any, index: any) => {
    const targetValue = e;
    if (code == 'quantity') {
      record['basic_quantity'] = targetValue * record['ratio'];
      record['quantity'] = targetValue;
      record['money'] = targetValue * record['price'];
    }
    setRowData([...rowData]);
  };
  const changeRatio = (e: any, record: any) => {
    let unitName = record.unit;
    record.units.forEach((item: any) => {
      if (item.value === e) {
        unitName = item.label;
      }
    });

    record.unit = unitName;
    record.ratio = e;
    record.price = e * record.basic_price;
    record.money = e * record.basic_price * record.quantity;
    record.basic_quantity = record.quantity * e;
    record['old_ratio'] = e;
    setRowData([...rowData]);
  };
  const handleClick = async () => {
    setIsLoading(true);
    const bool = await print({
      fid: fid,
    });
    setIsLoading(false);
    if (bool?.code === 0) {
      XlbPrintModal({
        data: bool?.data,
        title: '领用进出单打印模板',
      });
    }
  };
  const footerSet = () => {
    footerData[0] = {};
    footerData[0]._index = '合计';
    footerData[0].money = hasAuth(['领用申请单/成本价', '查询'])
      ? rowData
          .reduce(
            (sum: number, v: any) => sum + Number(v?.newRow ? 0 : v.money),
            0,
          )
          .toFixed(2)
      : '****';
    footerData[0].quantity = rowData
      .reduce(
        (sum: number, v: any) => sum + Number(v?.newRow ? 0 : v.quantity),
        0,
      )
      .toFixed(3);
    footerData[0].tax_money = hasAuth(['领用申请单/成本价', '查询'])
      ? rowData
          .reduce(
            (sum: number, v: any) => sum + Number(v?.newRow ? 0 : v.tax_money),
            0,
          )
          .toFixed(2)
      : '****';
    footerData[0].basic_quantity = rowData
      .reduce(
        (sum: number, v: any) => sum + Number(v?.newRow ? 0 : v.basic_quantity),
        0,
      )
      .toFixed(3);
    footerData[0].original_no_tax_cost_money = hasAuth([
      '领用申请单/成本价',
      '查询',
    ])
      ? rowData
          .reduce(
            (sum: number, v: any) =>
              sum + Number(v?.newRow ? 0 : v.original_no_tax_cost_money || 0),
            0,
          )
          .toFixed(2)
      : '****';
    footerData[0].original_cost_money = hasAuth(['领用申请单/成本价', '查询'])
      ? rowData
          .reduce(
            (sum: number, v: any) =>
              sum + Number(v?.newRow ? 0 : v.original_cost_money),
            0,
          )
          .toFixed(2)
      : '****';
    footerData[0].original_no_tax_cost_money = hasAuth([
      '领用申请单/成本价',
      '查询',
    ])
      ? rowData
          .reduce(
            (sum: number, v: any) =>
              sum + Number(v?.newRow ? 0 : (v.original_no_tax_cost_money ?? 0)),
            0,
          )
          .toFixed(2)
      : '****';
    setFooterData(footerData);
  };
  // 批量添加
  const confirmAdd = (list: any, add_type = 'item') => {
    let filterArr = [...list];
    const ids = rowData.map((v: any) => v.item_id);
    const repeatArr = list?.filter((v: any) =>
      ids.includes(add_type === 'import' ? v?.item_id : v.id),
    );
    const rName = [
      repeatArr.map(
        (v: any) => `【${add_type === 'import' ? v?.item_name : v.name}】`,
      ),
    ];
    if (repeatArr.length) {
      XlbTipsModal({
        tips: '以下商品已存在，不允许重复添加，系统已自动过滤！',
        tipsList: rName,
      });
    }
    filterArr = list.filter(
      (item: any) =>
        !ids.includes(add_type === 'import' ? item?.item_id : item.id),
    );

    const newList = filterArr?.map((v: any) => {
      return {
        ...v,
        units: Array.from(
          new Set([
            JSON.stringify({
              label: v.delivery_unit,
              value: v.delivery_ratio,
            }),
            JSON.stringify({ label: v.unit, value: 1 }),
          ]),
        ).map((item) => {
          return JSON.parse(item);
        }),
        ratio: v.delivery_ratio,
        item_id: v.id || v.item_id, // item_id 用于标识
        item_code: v.code || v.item_code, // 商品代码
        item_bar_code: v.bar_code || v.item_bar_code, // 商品条码
        item_name: v.name || v.item_name, // 商品名称
        item_spec: v.purchase_spec || v.item_spec, // 采购规格
        unit: v.delivery_unit, // 单位
        delivery_unit: v.delivery_unit, // 配送单位
        delivery_ratio: v.delivery_ratio, // 配送比例
        quantity: 0, // 数量
        price: v.basic_price * v.delivery_ratio, // 单价
        money: 0, // 金额
        basic_unit: v.basic_unit || v.unit, // 默认配送单位
        basic_quantity: 0, // 基本数量
        basic_price: v.basic_price,
        basic_cost_price: v.basic_cost_price,
        period: v.period || '', // 保质期
        basic_stock_quantity: v.basic_stock_quantity || 0, // 库存数量
        memo: v.memo || '',
        _click: false,
        _edit: false,
      };
    });
    newList.length > 0 ? setEdit(true) : setEdit(false);
    // 最后过滤掉没有item_id的
    const mergeArr = [...rowData, ...newList]?.filter((v) => v?.item_id);
    setRowData(
      mergeArr?.map((v, index) => ({ ...v, key: index + '_' + v?.item_id })),
    );
  };
  const tableRender = (item: any) => {
    switch (item.code) {
      case 'ratio':
        item.render = (value: any, record: any, index: number) => {
          return record._click && info.state === 'INIT' && record.item_code ? (
            <div onClick={(e) => e.stopPropagation()}>
              <XlbSelect
                width={80}
                options={record.units || []}
                defaultValue={value}
                allowClear={false}
                onChange={(e) => {
                  changeRatio(e, record);
                }}
              />
            </div>
          ) : (
            // 反显
            record?.units?.find((v: any) => v.value == record?.ratio)?.label
          );
        };
        break;
      case 'quantity':
        item.render = (value: any, record: any, index: any) => {
          return record?._click && info.state === 'INIT' && record.item_code ? (
            <XlbInputNumber
              style={{ width: 80 }}
              value={value}
              min={0}
              precision={3}
              onChange={(e) =>
                inputChange(e, item.code, record, index['index'])
              }
            ></XlbInputNumber>
          ) : (
            Number(value || 0)?.toFixed(3)
          );
        };
        break;
      case 'basic_quantity':
        item.render = (value: any, record: any, index: number) => {
          return Number(value || 0)?.toFixed(3);
        };
        break;
      case 'price':
      case 'basic_price':
      case 'basic_cost_price':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info">
              {hasAuth(['领用申请单/成本价', '查询']) && value !== '****'
                ? Number(value || 0)?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'money':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info">
              {hasAuth(['领用申请单/成本价', '查询']) && value !== '****'
                ? Number(value || 0)?.toFixed(2)
                : '****'}
            </div>
          );
        };
        break;
      case 'memo':
        item.render = (value: any, record: any, index: number) => {
          if (record?._click && record?.item_name) {
            return (
              <XlbInput
                id={item.code + '-' + index.toString()}
                onFocus={(e) => e.target.select()}
                maxLength={30}
                onChange={(e) => {
                  record.memo = e?.target?.value;
                }}
                onBlur={(e) => {}}
              />
            );
          } else {
            return <div className="info">{value}</div>;
          }
        };
        break;
    }
    return item;
  };
  const onChangeArea = async (e) => {
    form.setFieldsValue({
      requisition_org_name: '',
      requisition_org_id: null,
      store_id: null,
      storehouse_id: null,
      org_name: null,
      org_id: null,
      cargo_owner_id: null,
      cargo_owner_name: null,
    });
    setCargo_org_id(null);
  };
  const openStoreAreaModal = async () => {
    try {
      const res = await openTreeModal({
        zIndex: 2002,
        title: '选择部门', // 标题
        url: '/erp/hxl.erp.requisitionorder.deptinfos', // 请求地址
        dataType: 'lists',
        checkable: false, // 是否多选
        requestParams: {
          names: getOrgNames(),
        },
        defaultExpandKeys: [deptinfoId],
        selectedNodes: selectedNodesRef?.current,
        fieldName: {
          id: 'oid',
          parent_id: 'parent_id',
          name: 'name',
        },
        width: 360,
        renderInputValue: (data: any) => {
          return data.map((item: any) => item.poid_org_admin_name);
        },
      });
      if (res) {
        form.setFieldsValue({
          requisition_org_name: res?.[0].poid_org_admin_name,
          requisition_org_id: res?.[0].oid,
          store_id: null,
          storehouse_id: null,
          org_name: null,
          org_id: null,
          cargo_owner_id: null,
          cargo_owner_name: null,
        });
        setCargo_org_id(null);
        if (enable_cargo_owner) {
          getSecondOrgAndCargoOwner(res?.[0].poid_org_admin_name);
        }
        setDeptinfoId(res?.[0].oid);
        selectedNodesRef.current = res?.map((v) => {
          return {
            ...v,
            oid: v.oid,
            name: v.poid_org_admin_name,
          };
        });
      }
    } catch (error) {
      console.error('Error in handleOrg:', error);
    }
  };
  const readinfo = async (fid: any) => {
    setIsLoading(true);
    const res = await read({ fid });
    setIsLoading(false);
    if (res.code === 0) {
      form.setFieldsValue({
        ...res.data,
        store_id: [res?.data?.store_id],
        flag: res?.data?.flag ? 1 : 0,
        requisition_org_id: res?.data?.requisition_org_id,
      });
      setFid(res?.data?.fid);
      setInfo({ state: res?.data?.state });
      setFileList(res?.data?.files);
      setDeptinfoId(res?.data?.requisition_org_id);
      selectedNodesRef.current = [
        {
          oid: res?.data?.requisition_org_id,
          name: res?.data?.requisition_org_name,
        },
      ];
      setRowData(
        res?.data?.details?.map((v: any, index: any) => {
          return {
            ...v,
            key: index + '_' + v?.item_id,
            units: Array.from(
              new Set([
                JSON.stringify({
                  label: v.delivery_unit,
                  value: v.delivery_ratio,
                }),
                JSON.stringify({ label: v.basic_unit, value: 1 }),
                JSON.stringify({ label: v.unit, value: v.ratio }),
              ]),
            ).map((item) => {
              return JSON.parse(item);
            }),
          };
        }),
      );
      setPagin({ ...pagin, total: res?.data?.details?.length });
      getStoreHouse(res?.data?.store_id, false);
      if (enable_cargo_owner) {
        // 根据领用部门刷新cargo_org_id
        getSecondOrgAndCargoOwner(res?.data?.requisition_org_name, false);
      }
    }
  };
  const handleSave = async () => {
    try {
      await form.validateFields();
    } catch (err: any) {
      return false;
    }
    const validRows = rowData.filter((item: any) => !item._empty);
    if (
      validRows.some(
        (item: any) =>
          item.quantity === undefined ||
          item.quantity === null ||
          item.quantity === '',
      )
    ) {
      XlbTipsModal({
        tips: '请填写数量！',
      });
      return false;
    }
    const values = form.getFieldsValue(true);
    const cargo_owner_id = values?.cargo_owner_id;
    const data = {
      ...values,
      store_id: values.store_id?.[0],
      cargo_owner_id: Array.isArray(cargo_owner_id)
        ? cargo_owner_id[0]
        : cargo_owner_id,
      details: rowData.filter((item: any) => !item._empty),
      files: fileList,
    };
    setIsLoading(true);
    const res = fid == 1 ? await save(data) : await update(data);
    setIsLoading(false);
    if (res?.code === 0) {
      setEdit(false);
      setFid(res?.data?.fid);
      XlbMessage.success('保存成功');
      readinfo(res?.data?.fid);
    }
  };
  const handleAudit = async () => {
    try {
      await form.validateFields();
    } catch (err: any) {
      return false;
    }
    const validRows = rowData.filter((item: any) => !item._empty);
    if (
      validRows.some(
        (item: any) =>
          item.quantity === undefined ||
          item.quantity === null ||
          item.quantity === '',
      )
    ) {
      XlbTipsModal({
        tips: '请填写数量！',
      });
      return false;
    }
    const values = form.getFieldsValue(true);
    const cargo_owner_id = values?.cargo_owner_id;
    let data = {
      ...values,
      store_id: values.store_id?.[0],
      cargo_owner_id: Array.isArray(cargo_owner_id)
        ? cargo_owner_id[0]
        : cargo_owner_id,
      details: rowData.filter((item: any) => !item._empty),
      files: fileList,
    };
    setIsLoading(true);
    const resBeforeAudit = await check(data);
    let bool = true;
    if (resBeforeAudit?.code == 0 && resBeforeAudit?.data?.type == 0) {
      setRowData(resBeforeAudit?.data?.details);
      data = { ...data, details: resBeforeAudit?.data?.details };
      bool = await XlbTipsModal({
        tips: '',
        title: resBeforeAudit?.data?.memo,
        isConfirm: true,
        isCancel: false,
        tipsList: resBeforeAudit?.data?.tips,
      });
    }
    if (bool) {
      const res = await audit(data);
      setIsLoading(false);
      if (res?.code == 0) {
        setEdit(false);
        XlbMessage.success('操作成功');
        readinfo(fid);
        return;
      }
    } else {
      setIsLoading(false);
    }
  };
  const handleUnaudit = async (fid: any) => {
    setIsLoading(true);
    const res = await unaudit({ fid: fid });
    setIsLoading(false);
    if (res?.code === 0) {
      XlbMessage.success('反审核成功');
      readinfo(fid);
    }
  };
  const handleHandle = async (fid: any, type: boolean) => {
    setIsLoading(true);
    const res = await handle({ fid: fid, state: type ? 'PASS' : 'DENY' });
    setIsLoading(false);
    if (res?.code === 0) {
      XlbMessage.success('处理成功');
      readinfo(fid);
    }
  };
  const handleApprove = async (fid: any, type: boolean) => {
    setIsLoading(true);
    const res = await approve({ fid: fid, state: type ? 'APPROVE' : 'REFUSE' });
    setIsLoading(false);
    if (res?.code === 0) {
      XlbMessage.success('批复成功');
      readinfo(fid);
    }
  };
  const handleExport = async (e: any) => {
    setIsLoading(true);
    const res = await detailexport({ fid: fid });
    setIsLoading(false);
    if (res?.code === 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      XlbMessage.success('导出成功');
    }
  };
  const goBack = async () => {
    if (edit) {
      await XlbTipsModal({
        tips: '单据未保存，是否确认返回？',
        isCancel: true,
        onOkBeforeFunction: () => {
          onBack(true);
          return true;
        },
      });
      return false;
    }
    onBack(true);
  };
  const getOrgNames = () => {
    const queryOrgList = LStorage.get('userInfo')?.query_orgs || [];
    const orgIdList = LStorage.get('userInfo')?.org_ids || [];

    const orgNames = orgIdList
      .map((item) => queryOrgList?.find((org) => org.id === item))
      .filter(Boolean)
      .map((org) => org.name);

    return orgNames; // 返回机构名称数组
  };

  const getDept = async () => {
    const data = {
      names: getOrgNames(),
      phone: LStorage.get('userInfo').tel,
    };
    const res = await XlbFetch.post(
      '/erp/hxl.erp.requisitionorder.deptinfo',
      data,
    );
    form.setFieldsValue({
      requisition_org_name: res?.data?.poid_org_admin_name,
      requisition_org_id: res?.data?.oid,
    });
    // 开启多货主调用
    if (enable_cargo_owner) {
      getSecondOrgAndCargoOwner(res?.data?.poid_org_admin_name);
    }
  };
  useEffect(() => {
    getCollectReason();
    if (record.fid == 1) {
      setFid(1);
      getDept();
    } else {
      readinfo(record.fid);
    }
  }, []);
  useEffect(() => {
    footerSet();
    // 判断rowData是否存在一个数据有item_code
    if (rowData?.length > 0 && rowData?.some((item: any) => item?.item_code)) {
      setIsHasItemCode(true);
    } else {
      setIsHasItemCode(false);
    }
  }, [rowData]);
  return (
    <div
      style={{
        padding: 12,
        height: 'calc(100vh - 120px)',
        display: 'flex',
        flexDirection: 'column',
      }}
      className={style.itemConatainer}
    >
      <XlbButton.Group>
        {hasAuth(['领用申请单', '编辑']) && (
          <XlbButton
            label="保存"
            type="primary"
            disabled={info.state !== 'INIT' || isLoading}
            loading={isLoading}
            onClick={() => handleSave()}
            icon={<XlbIcon size={16} name="baocun" />}
          />
        )}
        {hasAuth(['领用申请单', '审核']) && (
          <XlbButton
            label="审核"
            type="primary"
            disabled={info.state !== 'INIT' || fid === 1 || isLoading}
            loading={isLoading}
            onClick={() => handleAudit()}
            icon={<XlbIcon size={16} name="shenhe" />}
          />
        )}
        {hasAuth(['领用申请单', '反审核']) && (
          <XlbButton
            label="反审核"
            type="primary"
            disabled={info.state !== 'AUDIT' || fid === 1 || isLoading}
            loading={isLoading}
            onClick={() => handleUnaudit(fid)}
            icon={<XlbIcon size={16} name="fanshenhe" />}
          />
        )}
        <XlbBaseUpload
          uploadText="附件"
          multiple={true}
          disabledDelete={false}
          disabled={false}
          mode="primaryButton"
          action="/erp/hxl.erp.requisitionorder.file.upload"
          listType={'table'}
          accept={'image'}
          data={{
            fid: fid,
          }}
          onChange={(e) => {
            setFileList(e);
          }}
          maxCount={99}
          fileList={fileList || []}
        />
        {hasAuth(['领用申请单', '处理']) && (
          <XlbDropdownButton
            label="处理"
            disabled={isLoading}
            dropList={
              hasAuth(['领用申请单', '处理'])
                ? [
                    {
                      label: '处理通过',
                      disabled: info.state !== 'AUDIT',
                    },
                    {
                      label: '处理拒绝',
                      disabled: info.state !== 'AUDIT',
                    },
                  ]
                : []
            }
            dropdownItemClick={(index: number, item: any) => {
              switch (item?.label) {
                case '处理通过':
                  handleHandle(fid, true);
                  break;
                case '处理拒绝':
                  handleHandle(fid, false);
                  break;
              }
            }}
          />
        )}
        {hasAuth(['领用申请单', '批复']) && (
          <XlbDropdownButton
            label="批复"
            disabled={isLoading}
            dropList={
              hasAuth(['领用申请单', '批复'])
                ? [
                    {
                      label: '批复通过',
                      disabled: info.state !== 'PASS',
                    },
                    {
                      label: '批复拒绝',
                      disabled: info.state !== 'PASS',
                    },
                  ]
                : []
            }
            dropdownItemClick={(index: number, item: any) => {
              switch (item?.label) {
                case '批复通过':
                  handleApprove(fid, true);
                  break;
                case '批复拒绝':
                  handleApprove(fid, false);
                  break;
              }
            }}
          />
        )}
        {(hasAuth(['领用申请单', '导出']) ||
          hasAuth(['领用申请单', '打印'])) && (
          <XlbDropdownButton
            label="业务操作"
            disabled={isLoading}
            dropList={[
              ...(hasAuth(['领用申请单', '导出'])
                ? [
                    {
                      label: '导出',
                      disabled: fid == 1,
                    },
                  ]
                : []),
              ...(hasAuth(['领用申请单', '打印'])
                ? [
                    {
                      label: '打印',
                      disabled: fid == 1,
                    },
                  ]
                : []),
            ]}
            dropdownItemClick={(index: number, item: any, e: any) => {
              switch (item?.label) {
                case '导出':
                  handleExport(e);
                  break;
                case '打印':
                  handleClick();
                  break;
              }
            }}
          />
        )}
        <XlbButton
          label="返回"
          type="primary"
          onClick={() => goBack()}
          icon={<XlbIcon size={16} name="fanhui" />}
        />
      </XlbButton.Group>
      <XlbBasicForm
        style={{ paddingLeft: '12px' }}
        colon
        form={form}
        disabled={isLoading}
        autoComplete="off"
        layout="inline"
        initialValues={{
          flag: 1,
        }}
      >
        <XlbTabs
          defaultActiveKey="baseInfo"
          activeKey={baseInfoActiveKey}
          onChange={(e) => setBaseInfoActiveKey(e)}
          items={[
            {
              label: '基本信息',
              key: 'baseInfo',
              children: (
                <div className="row-flex">
                  <div
                    className="row-flex"
                    style={{ flex: 1, flexWrap: 'wrap' }}
                  >
                    <XlbBasicForm.Item
                      label="领用部门"
                      name={'requisition_org_name'}
                      rules={[{ required: true, message: '请选择领用部门' }]}
                      shouldUpdate
                    >
                      <XlbInput
                        style={{ width: 180 }}
                        allowClear
                        disabled={info?.state !== 'INIT' || isHasItemCode}
                        suffix={
                          <span
                            style={{
                              display: 'inline-block',
                            }}
                            onClick={openStoreAreaModal}
                          >
                            <XlbIcon name="sousuo" size={14} color="#c9cdd4" />
                          </span>
                        }
                        onFocus={(e) => e.target.blur()}
                        onClick={openStoreAreaModal}
                        onChange={onChangeArea}
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item
                      noStyle
                      dependencies={['requisition_org_name']}
                    >
                      {({ getFieldValue }) => (
                        <>
                          <XlbBasicForm.Item
                            label="发货门店"
                            name="store_id"
                            rules={[
                              { required: true, message: '请选择发货门店' },
                            ]}
                          >
                            <XlbInputDialog
                              width={180}
                              disabled={
                                isHasItemCode ||
                                !getFieldValue('requisition_org_name')
                              }
                              dialogParams={{
                                type: 'store',
                                dataType: 'lists',
                                data: {
                                  exclude_org_manage_store: true,
                                  center_flag: true,
                                  org_ids: cargo_org_id
                                    ? [cargo_org_id]
                                    : undefined,
                                },
                              }}
                              fieldNames={{
                                idKey: 'id',
                                nameKey: 'store_name',
                              }}
                              handleValueChange={(value: any, options: any) => {
                                if (options?.length) {
                                  form.setFieldsValue({
                                    org_name: options[0]?.org_store_name,
                                    org_id: options[0]?.org_store_id,
                                    store_id: [options?.[0]?.id],
                                    storehouse_id: undefined,
                                  });
                                  getStoreHouse(options?.[0]?.id, true);
                                  if (!cargo_org_id && enable_cargo_owner) {
                                    form.setFieldsValue({
                                      cargo_owner_id: undefined,
                                      cargo_owner_name: undefined,
                                    });
                                    getEnableCargoOwner(options?.[0]?.id);
                                  }
                                }
                                if (!value && !options) {
                                  form.setFieldsValue({
                                    storehouse_id: undefined,
                                  });
                                  setStoreHouse([]);
                                }
                              }}
                            ></XlbInputDialog>
                          </XlbBasicForm.Item>
                          <XlbBasicForm.Item
                            label="仓库"
                            name="storehouse_id"
                            rules={[{ required: true, message: '请选择仓库' }]}
                          >
                            <XlbSelect
                              style={{ width: '180px' }}
                              options={storeHouse}
                              disabled={
                                isHasItemCode ||
                                !getFieldValue('requisition_org_name')
                              }
                              placeholder="请选择"
                              allowClear={false}
                            />
                          </XlbBasicForm.Item>
                          <XlbBasicForm.Item
                            hidden
                            label="发货组织"
                            name="org_id"
                          >
                            <XlbInput disabled style={{ width: '180px' }} />
                          </XlbBasicForm.Item>
                          {enable_organization ? (
                            <XlbBasicForm.Item label="发货组织" name="org_name">
                              <XlbInput disabled style={{ width: '180px' }} />
                            </XlbBasicForm.Item>
                          ) : null}{' '}
                          {!cargo_org_id && enable_cargo_owner ? (
                            <XlbBasicForm.Item
                              label="货主"
                              name="cargo_owner_id"
                              rules={[
                                { required: true, message: '请选择货主' },
                              ]}
                            >
                              <XlbInputDialog
                                dialogParams={{
                                  type: 'cargoOwner',
                                  isMultiple: false,
                                  isLeftColumn: false,
                                  data: {
                                    store_ids: getFieldValue('store_id'),
                                    filterSupplier: true,
                                    owner_type: 'ORGANIZATION',
                                  },
                                }}
                                fieldNames={{
                                  idKey: 'id',
                                  nameKey: 'source_name',
                                }}
                                width={180}
                                disabled={
                                  isHasItemCode ||
                                  isLoading ||
                                  !form?.getFieldValue('store_id') ||
                                  disabledCargoOwner
                                }
                                placeholder="请选择"
                                handleValueChange={(e: any, option: any) => {
                                  if (option?.length > 0) {
                                    form.setFieldsValue({
                                      cargo_owner_id: option?.[0]?.id,
                                      cargo_owner_name:
                                        option?.[0]?.source_name,
                                    });
                                  }
                                }}
                              />
                            </XlbBasicForm.Item>
                          ) : enable_cargo_owner ? (
                            <XlbBasicForm.Item
                              label="货主"
                              name="cargo_owner_name"
                            >
                              <XlbInput disabled style={{ width: '180px' }} />
                            </XlbBasicForm.Item>
                          ) : null}
                        </>
                      )}
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item
                      label="进出方向"
                      name="flag"
                      rules={[{ required: true, message: '请选择进出方向' }]}
                    >
                      <XlbSelect
                        disabled
                        style={{ width: '180px' }}
                        allowClear={false}
                        options={[
                          {
                            label: '退货',
                            value: 0,
                          },
                          {
                            label: '发货',
                            value: 1,
                          },
                        ]}
                      ></XlbSelect>
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item
                      label="领用原因"
                      name={'requisition_reason_id'}
                      rules={[{ required: true, message: '请选择领用原因' }]}
                    >
                      <XlbSelect
                        style={{ width: '180px' }}
                        options={collectReason}
                        placeholder="请选择"
                        disabled={info?.state !== 'INIT'}
                      ></XlbSelect>
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="单据号" name="fid">
                      <XlbInput
                        size="small"
                        style={{ width: '180px' }}
                        disabled
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="商品部门" name="item_dept_names">
                      <XlbInput
                        size="small"
                        style={{ width: '180px' }}
                        disabled
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="留言备注" name="memo">
                      <XlbInput
                        style={{ width: 470 }}
                        maxLength={50}
                        placeholder="请输入"
                        disabled={
                          !hasAuth(['领用申请单', '编辑']) ||
                          info.state !== 'INIT'
                        }
                      />
                    </XlbBasicForm.Item>
                  </div>
                  {info?.state && (
                    <div
                      style={{
                        width: '150px',
                        flexBasis: '150px',
                        display: 'flex',
                        justifyContent: 'center',
                      }}
                    >
                      <img
                        src={orderStatusIcons[info?.state]}
                        width={86}
                        height={78}
                      />
                    </div>
                  )}
                </div>
              ),
            },
            {
              label: '其他信息',
              key: 'otherInfo',
              children: (
                <>
                  <div className="row-flex">
                    <XlbBasicForm.Item label="制单人" name="create_by">
                      <XlbInput style={{ width: '180px' }} disabled />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="制单时间" name="create_time">
                      <XlbInput style={{ width: '180px' }} disabled />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="审核人" name="audit_by">
                      <XlbInput style={{ width: '180px' }} disabled />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="审核时间" name="audit_time">
                      <XlbInput style={{ width: '180px' }} disabled />
                    </XlbBasicForm.Item>
                  </div>
                  <div className="row-flex">
                    <XlbBasicForm.Item label={'修改人'} name={'update_by'}>
                      <XlbInput style={{ width: '180px' }} disabled />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label={'修改时间'} name={'update_time'}>
                      <XlbInput style={{ width: '180px' }} disabled />
                    </XlbBasicForm.Item>
                  </div>
                </>
              ),
            },
          ]}
        />
      </XlbBasicForm>

      <XlbButton.Group>
        {hasAuth(['领用申请单', '编辑']) && (
          <XlbButton
            label="批量添加"
            type="primary"
            disabled={info.state !== 'INIT' || !form.getFieldValue('store_id')}
            onClick={async () => {
              const list = await XlbBasicData({
                type: 'goods',
                url: '/erp/hxl.erp.requisitionorder.item.page',
                isMultiple: true,
                dataType: 'lists',
                primaryKey: 'id',
                resetForm: true,
                nullable: false,
                data: {
                  store_id: form.getFieldValue('store_id')?.[0],
                  storehouse_id: form.getFieldValue('storehouse_id'),
                  cargo_owner_id: enable_cargo_owner
                    ? Array.isArray(form.getFieldValue('cargo_owner_id'))
                      ? form.getFieldValue('cargo_owner_id')?.[0]
                      : form.getFieldValue('cargo_owner_id')
                    : null,
                },
              });
              if (!list) return;
              if (Array.isArray(list)) {
                confirmAdd(list, 'item');
              }
            }}
            icon={<XlbIcon size={16} name="jia" />}
          />
        )}
      </XlbButton.Group>

      <XlbShortTable
        isLoading={isLoading}
        showSearch
        key={baseInfoActiveKey}
        style={{ flex: 1, margin: '8px 0 0' }}
        url={'/erp/hxl.erp.requisitionorder.item.page'}
        data={{
          store_id: form.getFieldValue('store_id')?.[0],
          storehouse_id: form.getFieldValue('storehouse_id'),
          cargo_owner_id: enable_cargo_owner
            ? Array.isArray(form.getFieldValue('cargo_owner_id'))
              ? form.getFieldValue('cargo_owner_id')?.[0]
              : form.getFieldValue('cargo_owner_id')
            : null,
        }}
        onChangeData={(data: any[], type: string) => {
          const originIds = rowData.map((v: any) => v.item_id);
          const initialValues = Object.freeze({
            quantity: 0,
            memo: null,
          });
          const getInitials = (v: any) =>
            originIds.includes(v.item_id) ? {} : initialValues;

          setRowData(
            data.map((v, index) => {
                const itemId = v.id || v.item_id;
                const oldItem = rowData.find(
                  (item: any) => item.item_id === itemId,
                );
                if (oldItem) {
                  // 已存在，直接用老的
                  return oldItem;
                }
              return {
                // 映射字段
                ...v,
                units: Array.from(
                  new Set([
                    JSON.stringify({
                      label: v.delivery_unit,
                      value: v.delivery_ratio,
                    }),
                    JSON.stringify({ label: v.unit, value: 1 }),
                  ]),
                ).map((item) => {
                  return JSON.parse(item);
                }),
                key: `${index}_${v.id || v.item_id}`, // 唯一的行 key
                ratio: v.old_ratio || v.delivery_ratio,
                item_id: v.id || v.item_id, // item_id 用于标识
                item_code: v.code || v.item_code, // 商品代码
                item_bar_code: v.bar_code || v.item_bar_code, // 商品条码
                item_name: v.name || v.item_name, // 商品名称
                item_spec: v.purchase_spec || v.item_spec, // 采购规格
                unit: v.delivery_unit, // 单位
                delivery_unit: v.delivery_unit, // 配送单位
                delivery_ratio: v.delivery_ratio, // 配送比例
                quantity: v.quantity || 0, // 数量
                price: v.old_ratio
                  ? v.basic_price * v.old_ratio
                  : v.basic_price * v.delivery_ratio || 0, // 单价
                money: v.money || 0, // 金额
                basic_unit: v.basic_unit || v.unit, // 默认配送单位
                basic_quantity: v.old_ratio
                  ? v.quantity * v.old_ratio
                  : v.quantity * v.delivery_ratio || 0, // 基本数量
                basic_price: v.basic_price,
                period: v.period || '', // 保质期
                basic_stock_quantity: v.basic_stock_quantity || 0, // 库存数量
                memo: v.memo || '',
                _click: false,
                _edit: false,
                ...getInitials(v), // 初始化值
              };
            }),
          );
          setEdit(type !== 'onChangeSorts');
        }}
        disabled={
          info.state !== 'INIT' ||
          !hasAuth(['领用申请单', '编辑']) ||
          !form.getFieldValue('store_id')
        }
        columns={itemArrdetail?.map((v) => tableRender(v))}
        dataSource={rowData}
        footerDataSource={footerData}
        total={rowData?.length}
        selectMode="single"
        primaryKey="item_id"
        // popoverPrimaryKey="id"
      />
    </div>
  );
};
export default Item;
