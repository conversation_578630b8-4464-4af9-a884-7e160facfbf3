import { FORM_TYPE_LIST } from '@/pages/purchase/mustSellGoodsManagement/data';
import { SearchFormType, XlbTableColumnProps } from '@xlb/components';

export const FORM_LIST: SearchFormType[] = [
  {
    type: 'compactDatePicker',
    label: '创建日期',
    name: 'create_date',
    allowClear: false,
  },
  {
    label: '所属组织',
    name: 'org_id',
    type: 'select',
    check: true,
    // @ts-ignore
    onChange: (_e: any, form: any) => {
      form?.setFieldsValue({ store_ids: [] });
    },
    selectRequestParams: {
      url: '/erp-mdm/hxl.erp.org.find',
      responseTrans(response: any) {
        return (
          response
            ?.filter((item: any) => item.level === 2)
            ?.map((item: any) => ({
              ...item,
              label: item.name,
              value: item.id,
            })) || []
        );
      },
    },
  },
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    allowClear: false,
    dependencies: ['org_ids'],
    dialogParams: (data) => ({
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: { org_ids: data.org_ids, status: true },
    }),
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '商品',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isMultiple: true,
    },
  },
  {
    label: '采购类型',
    name: 'purchase_type',
    type: 'select',
    clear: true,
    check: true,
    options: FORM_TYPE_LIST,
  },
  {
    label: '商品分类',
    name: 'category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      // @ts-ignore
      title: '选择商品分类', // 标题
      url: '/erp/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      params: {
        limit_level: 1,
      },
      width: 360, // 模态框宽度
    },
  },
];

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center',
  },
  {
    name: '月份',
    code: 'month',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '组织',
    code: 'org_name',
    width: 200,
    features: { sortable: true },
  },
  {
    name: '门店',
    code: 'store_name',
    width: 200,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    features: { sortable: true },
    width: 200,
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 200,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'purchase_spec',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '采购类型',
    code: 'purchase_type_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '商品分类',
    code: 'item_category_name',
    width: 160,
  },
];
