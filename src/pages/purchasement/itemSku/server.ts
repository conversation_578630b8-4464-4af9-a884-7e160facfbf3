import { XlbFetch as ErpRequest } from '@xlb/utils';
// 查询
const getLatestPrice = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.item.summary', { data })
}
//  账号管理仓库查询
const getStock = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storehouse.store.find', { data })
}
//  账号管理仓库查询
const getRead = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.item.summary.read', { data })
}
// 保存sku数
const saveSummaryNum = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.item.summary.initial.save', { data })
}

const fetchStoreHouse = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storehouse.store.find', { ...data })
}
const fetchSummaryDetail = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.item.summary.read', { ...data })
}


export default {
  getLatestPrice,
  getStock,
  getRead,
  saveSummaryNum,
  fetchStoreHouse,
  fetchSummaryDetail
}