import { columnWidthEnum } from '@/data/common/constant';
import { SearchFormType, XlbTableColumnProps } from '@xlb/components';
//进出方向
export const InOutDirection = [
  {
    label: '出库',
    value: 'false',
  },
  {
    label: '入库',
    value: 'true',
  },
];
//单据类型
export const Billstype = [
  {
    label: '采购收货单',
    value: '收货单',
  },
  {
    label: '采购退货单',
    value: '退货单',
  },
  {
    label: '调出单',
    value: '调出单',
  },
  {
    label: '调入单',
    value: '调入单',
  },
  {
    label: '批发销售单',
    value: '批发销售单',
  },
  {
    label: '批发退货单',
    value: '批发退货单',
  },
  {
    label: '库存盘点单',
    value: '库存盘点',
  },
  {
    label: '库存调整单',
    value: '库存调整',
  },
  {
    label: '同店转仓单',
    value: '同店转仓',
  },
  {
    label: '库存成本调整单',
    value: '库存成本调整',
  },
  {
    label: '前台销售单',
    value: '前台销售',
  },
  {
    label: '积分兑换',
    value: '积分兑换',
  },
  {
    label: '领用进出单',
    value: '领用进出单',
  },
];

//查询单位
export const serachOptions = [
  {
    label: '基本单位',
    value: 'BASIC',
  },
  {
    label: '采购单位',
    value: 'PURCHASE',
  },
  {
    label: '配送单位',
    value: 'DELIVERY',
  },
  {
    label: '库存单位',
    value: 'STOCK',
  },
  {
    label: '批发单位',
    value: 'WHOLESALE',
  },
];

export const searchFormList: SearchFormType[] = [
  {
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'compactDatePicker',
    allowClear: false,
    width: 392,
    showTime: true,
    resultFormat: 'YYYY-MM-DD HH:mm',
    format: 'YYYY-MM-DD HH:mm',
  },
  {
    label: '时间类型',
    name: 'time_type',
    type: 'select',
    allowClear: false,
    clear: false,
    check: true,
    options: [
      {
        label: '进出时间',
        value: 'create_date',
      },
      {
        label: '单据日期',
        value: 'operate_date',
      },
    ],
  },
  {
    label: '查询模式',
    name: 'query_mode',
    type: 'select',
    check: true,
    allowClear: false,
    initialValue: 0,
    options: [
      {
        label: '明细',
        value: 0,
      },
      {
        label: '汇总',
        value: 1,
      },
    ],
  },
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        status: true,
      },
      nullable: false,
    },
    allowClear: false,
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'store_name',
    },
    removeIcon: null,
  },
  {
    label: '仓库',
    name: 'storehouse_ids',
    type: 'select',
    multiple: false,
    dependencies: ['store_ids'],
    disabled: (form) => {
      const store_ids = form.getFieldValue('store_ids');
      return !store_ids || store_ids?.length > 1;
    },
    handleDefaultValue: (data: any, formData: any) => {
      if (data?.length === 0) {
        return null;
      }
      const defaultStoreHouse =
        data.find((item: any) => item.default_flag) || data[0];
      return defaultStoreHouse?.value;
    },
    // @ts-ignore
    selectRequestParams: (params: any, form) => {
      form?.setFieldsValue({
        storehouse_ids: null,
      });
      if (params?.store_ids?.length === 1) {
        return {
          url: '/erp/hxl.erp.storehouse.store.find',
          postParams: {
            store_id: params?.store_ids?.[0],
          },
          responseTrans(data: any) {
            const options = data.map((item: any) => ({
              label: item.name,
              value: item.id,
              default_flag: item.default_flag,
            }));
            return options;
          },
        };
      }
    },
  },
  {
    label: '货主',
    name: 'cargo_owner_ids',
    type: 'inputDialog',
    hidden: false,
    dialogParams: {
      type: 'cargoOwner',
      dataType: 'lists',
      isLeftColumn: false,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
    allowClear: false,
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'source_name',
    },
  },
  {
    label: '单据类型',
    name: 'order_types',
    type: 'select',
    clear: false,
    check: true,
    multiple: true,
    options: Billstype,
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '商品类别',
    name: 'item_category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择商品分类', // 标题
      url: '/erp-mdm/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '进出方向',
    name: 'flag',
    type: 'select',
    clear: true,
    check: true,
    options: InOutDirection,
  },
  {
    label: '关键字',
    name: 'keyword',
    type: 'input',
    clear: true,
    check: true,
  },
  {
    label: '单据号',
    name: 'order_fid',
    type: 'input',
    clear: true,
    check: true,
  },
  {
    label: '批次号',
    name: 'batch_number',
    type: 'input',
    clear: true,
    check: true,
  },
  {
    label: '生产日期',
    name: 'producing_date',
    type: 'datePicker',
    clear: true,
    check: true,
  },
  {
    label: '到期日期',
    name: 'expire_date',
    type: 'datePicker',
    clear: true,
    check: true,
  },
  {
    label: '业财核算分类',
    name: 'finance_code',
    type: 'select',
    disabled: false,
    clear: true,
    selectRequestParams: () => {
      return {
        url: '/erp-mdm/hxl.erp.settlementcategory.center.find',
        postParams: {},
        responseTrans(data: any) {
          const options = data.map((item: any) => ({
            label: item.category_name,
            value: item.code,
          }));
          return options;
        },
      };
    },
  },
  {
    label: '查询单位',
    name: 'unit_type',
    type: 'select',
    clear: true,
    check: true,
    options: serachOptions,
  },
];

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '仓库',
    code: 'storehouse_name',
    width: 120,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '货主',
    code: 'cargo_source_name',
    hiddenInXlbColumns: true,
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '单据类型',
    code: 'order_type',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单据号',
    code: 'order_fid',
    width: 190,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '进出时间',
    code: 'create_time',
    width: 220,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '单据日期',
    code: 'operate_date',
    width: columnWidthEnum.TIME,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '批次号',
    code: 'batch_number',
    width: 120,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '生产日期',
    code: 'producing_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '到期日期',
    code: 'expire_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '进出基本数量',
    code: 'basic_quantity',
    width: 130,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进出基本单价',
    code: 'basic_price',
    width: 130,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进出金额',
    code: 'money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进出成本基本单价',
    code: 'basic_cost_price',
    width: 150,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进出成本基本单价（去税）',
    code: 'basic_no_tax_cost_price',
    width: 220,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进出成本金额',
    code: 'no_adjust_cost_money',
    width: 130,
    features: { sortable: true },
    align: 'right',
  },

  {
    name: '进出成本金额（去税）',
    code: 'no_tax_cost_money',
    width: 180,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进出单位',
    code: 'unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '进出数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进出单价',
    code: 'price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进出成本单价',
    code: 'cost_price',
    width: 130,
    features: { sortable: true },
    align: 'right',
  },
  // {
  //   name: '进出成本单价(去税)',
  //   code: 'no_tax_cost_price',
  //   width: 200,
  //   features: { sortable: true },
  //   align: 'right'
  // },
  {
    name: '进出后基本单位数量',
    code: 'balance_basic_quantity',
    width: 165,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进出后库存单位数量',
    code: 'balance_stock_quantity',
    width: 165,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进出后成本金额',
    code: 'balance_money',
    width: 140,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '调整成本金额',
    code: 'adjust_money',
    width: 130,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '调整成本金额(去税)',
    code: 'no_tax_adjust_money',
    width: 180,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left',
  },
];

export const tableList1: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '货主',
    code: 'cargo_source_name',
    hiddenInXlbColumns: true,
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '基本单位',
    code: 'item_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  // {
  //   name: '库存单位',
  //   code: 'stock_unit',
  //   width: 100,
  //   features: { sortable: true },
  //   align: 'left'
  // },
  // {
  //   name: '进项基本数量',
  //   code: 'in_basic_quantity',
  //   width: 120,
  //   features: { sortable: true },
  //   align: 'left'
  // },
  // {
  //   name: '进项库量',
  //   code: 'in_quantity',
  //   width: 100,
  //   features: { sortable: true },
  //   align: 'left'
  // },
  // {
  //   name: '出项基本数量',
  //   code: 'out_basic_quantity',
  //   width: 120,
  //   features: { sortable: true },
  //   align: 'left'
  // },
  // {
  //   name: '出项库量',
  //   code: 'out_quantity',
  //   width: 100,
  //   features: { sortable: true },
  //   align: 'right'
  // },
  {
    name: '进出单位',
    code: 'unit',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '进项基本数量',
    code: 'in_basic_quantity',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进项库量',
    code: 'in_quantity',
    width: 100,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '出项基本数量',
    code: 'out_basic_quantity',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '出项库量',
    code: 'out_quantity',
    width: 100,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进出基本数量',
    code: 'basic_quantity',
    width: 130,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进出基本单价',
    code: 'basic_price',
    width: 130,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进出金额',
    code: 'money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进出成本基本单价',
    code: 'cost_price',
    width: 150,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进出成本金额',
    code: 'no_adjust_cost_money',
    width: 130,
    features: { sortable: true },
    align: 'right',
  },
  // {
  //   name: '进出成本基本单价(去税)',
  //   code: 'no_tax_cost_price',
  //   width: 220,
  //   features: { sortable: true },
  //   align: 'right'
  // },
  // {
  //   name: '进出成本金额(去税)',
  //   code: 'no_tax_cost_money',
  //   width: 200,
  //   features: { sortable: true },
  //   align: 'right'
  // },
  {
    name: '调整成本金额',
    code: 'adjust_money',
    width: 130,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '调整成本金额(去税)',
    code: 'no_tax_adjust_money',
    width: 180,
    features: { sortable: true },
    align: 'right',
  },
];