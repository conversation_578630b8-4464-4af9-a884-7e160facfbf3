import { XlbFetch } from '@xlb/utils';

// 分页查询
export const getList = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.unsalableitem.find', { ...data });
};

export const getStoreHouse = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.storehouse.store.find', { ...data });
};

export const getExport = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.unsalableitem.export', { ...data });
};
