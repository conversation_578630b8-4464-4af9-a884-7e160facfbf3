// @ts-ignore
/* eslint-disable */
// import { request } from '@umijs/max';
import { XlbFetch } from '@xlb/utils';
/** 查询 POST /tms/hxl.wms.storehouse.default.find */
export async function accountLoginUsingPost(
  body: any,
  options?: { [key: string]: any },
) {
  return XlbFetch('/erp/hxl.erp.user.account.login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
// 不走下载中心的下载
export async function exportPage(
  url: string,
  body: any,
  options?: { [key: string]: any },
) {
  return XlbFetch<any>(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}